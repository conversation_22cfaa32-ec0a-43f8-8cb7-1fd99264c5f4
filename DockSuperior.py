from PyQt6.QtWidgets import QWidget, QHBoxLayout, QPushButton, QSizePolicy, QSpacerItem, QGraphicsDropShadowEffect, QLabel
from PyQt6.QtCore import QSize, Qt, QTimer, QTime, QPoint
from PyQt6.QtGui import QPixmap, QMouseEvent, QColor
import os
from CREAR import CustomCloseButton, CustomMaximizeButton, CustomMinimizeButton

# Verificar si psutil está disponible para el widget unificado
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("psutil no está disponible. Algunas funciones del sistema pueden estar limitadas.")

class DockSuperior(QWidget):
    def __init__(self, parent_widget=None):
        super().__init__(parent_widget)
        self.parent_widget = parent_widget
        self.dragging = False
        self.offset = QPoint()
        self.setFixedHeight(45)
        self.setup_ui()
        
    def setup_ui(self):
        """Configura toda la interfaz de usuario del dock"""
        # Widget de fondo transparente (sin fondo oscuro)
        self.background_widget = QWidget(self)
        self.background_widget.setStyleSheet("""
            background: transparent;
            border: none;
        """)
        
        self.background_widget.lower()
        margin = 2
        self.background_widget.setGeometry(
            margin, 
            margin, 
            self.width() - (margin * 2), 
            self.height() - (margin * 2)
        )
        
        # Layout principal - márgenes verticales reducidos para pegar más al borde
        dock_layout = QHBoxLayout(self)
        dock_layout.setContentsMargins(15, 4, 15, 4)
        dock_layout.setSpacing(12)
        
        # Sección izquierda: Información de la app
        self.setup_app_info_section(dock_layout)
        
        # Espaciador central
        dock_layout.addItem(QSpacerItem(0, 0, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        
        # Sección central: Reloj y temperatura
        self.setup_clock_temp_section(dock_layout)
        
        # Espaciador
        dock_layout.addItem(QSpacerItem(0, 0, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        
        # Sección derecha: Botones de control
        self.setup_window_controls(dock_layout)

    def setup_app_info_section(self, layout):
        """Configura la sección con información de la aplicación"""
        app_info_widget = QWidget()
        app_info_widget.setStyleSheet("background: transparent; border: none;")
        
        app_info_layout = QHBoxLayout(app_info_widget)
        app_info_layout.setContentsMargins(0, 0, 0, 0)
        app_info_layout.setSpacing(10)
        
        # Icono de la aplicación usando Logo_ZConver - más pequeño
        from CREAR import Logo_ZConver
        icon_label = QLabel()
        logo_icon = Logo_ZConver(24)
        icon_pixmap = logo_icon.pixmap(24, 24)
        icon_label.setPixmap(icon_pixmap)
        icon_label.setStyleSheet("background: transparent; border: none;")
        
        # Texto de la versión
        app_version = "0.0.1"
        self.text_label = QLabel(f"ZConver v{app_version}")
        self.text_label.setStyleSheet("""
            color: white; 
            font-size: 13px; 
            font-weight: bold;
            background: transparent;
            border: none;
        """)
        
        app_info_layout.addWidget(icon_label)
        app_info_layout.addWidget(self.text_label)
        layout.addWidget(app_info_widget)

    def setup_clock_temp_section(self, layout):
        """Configura la sección del sistema unificada (reloj, CPU y disco)"""
        # Widget unificado de información del sistema con monitor con hilos habilitado
        from system_info_widget import SystemInfoWidget
        self.system_info_widget = SystemInfoWidget(self, use_threaded_monitor=True)
        layout.addWidget(self.system_info_widget)

    def setup_window_controls(self, layout):
        """Configura los botones de control de ventana"""
        controls_widget = QWidget()
        controls_widget.setStyleSheet("background: transparent; border: none;")
        
        controls_layout = QHBoxLayout(controls_widget)
        controls_layout.setContentsMargins(0, 0, 0, 0)
        controls_layout.setSpacing(3)
        
        # Crear botones personalizados
        self.minimizeButton = CustomMinimizeButton(self)
        self.maximizeButton = CustomMaximizeButton(self)
        self.closeButton = CustomCloseButton(self)
        
        # Conectar señales
        self.minimizeButton.clicked.connect(self.parent_widget.showMinimized)
        self.maximizeButton.clicked.connect(self.parent_widget.toggleMaximizeRestore)
        self.closeButton.clicked.connect(self.parent_widget.close)
        
        controls_layout.addWidget(self.minimizeButton)
        controls_layout.addWidget(self.maximizeButton)
        controls_layout.addWidget(self.closeButton)
        
        layout.addWidget(controls_widget)

    def resizeEvent(self, event):
        """Ajusta el widget de fondo al redimensionar"""
        super().resizeEvent(event)
        margin = 2
        self.background_widget.setGeometry(
            margin, 
            margin, 
            self.width() - (margin * 2), 
            self.height() - (margin * 2)
        )

    def mousePressEvent(self, event: QMouseEvent):
        """Maneja el inicio del arrastre de ventana"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = True
            self.offset = event.position().toPoint()

    def mouseMoveEvent(self, event: QMouseEvent):
        """Maneja el arrastre de ventana"""
        if self.dragging and self.parent_widget:
            new_pos = self.parent_widget.pos() + event.position().toPoint() - self.offset
            self.parent_widget.move(new_pos)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """Finaliza el arrastre de ventana"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False

    def update_clock_widget_color(self, color):
        """Método mantenido por compatibilidad pero ya no cambia el color"""
        # El widget unificado mantiene sus propios colores
        pass

    def update_text_color(self, is_light_mode):
        """Actualiza los colores de texto según el modo de interfaz"""
        text_color = "rgba(50, 50, 50, 255)" if is_light_mode else "white"
        self.text_label.setStyleSheet(f"""
            color: {text_color}; 
            font-size: 13px; 
            font-weight: bold;
            background: transparent;
            border: none;
        """)
        if hasattr(self, 'system_info_widget'):
            self.system_info_widget.update_text_color(is_light_mode)

    def update_background_color(self, is_light_mode):
        """Actualiza el color de fondo según el modo de interfaz"""
        bg_color = "rgba(200, 200, 200, 120)" if is_light_mode else "rgba(80, 80, 80, 120)"
        self.background_widget.setStyleSheet(f"""
            background-color: {bg_color};
            border-radius: 10px;
            border: none;
        """)

    def set_disk_path(self, path):
        """Establece la ruta del disco a monitorear en el widget unificado"""
        if hasattr(self, 'system_info_widget'):
            self.system_info_widget.set_disk_path(path)