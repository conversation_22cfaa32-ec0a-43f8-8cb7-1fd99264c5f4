import os
import json
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QPushButton, QLabel, 
                             QFileDialog, QMessageBox, QHBoxLayout, 
                             QGraphicsDropShadowEffect, QFrame)
from PyQt6.QtGui import QIcon, QColor, QPainter, QCursor
from PyQt6.QtCore import Qt, QSize, QPropertyAnimation, pyqtProperty, QRectF

def get_icon_path(icon_name):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    icon_path = os.path.join(current_dir, 'ICONOS', icon_name)
    if not os.path.exists(icon_path):
        print(f"Advertencia: No se pudo encontrar el icono: {icon_path}")
    return icon_path

class AnimatedButton(QPushButton):
    def __init__(self, icon_name, tooltip, click_handler=None, parent=None):
        super().__init__(parent)
        self.setStyleSheet("QPushButton {border: none; background: transparent;}")
        self._icon_size = 25
        self.setFixedSize(35, 35)
        icon_path = get_icon_path(icon_name)
        self.icon = QIcon(icon_path)
        self.setMouseTracking(True)
        self.setToolTip(tooltip)
        self.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        if click_handler:
            self.clicked.connect(click_handler)
        self.add_shadow_effect(Qt.GlobalColor.white)

    def add_shadow_effect(self, color):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(color)
        self.setGraphicsEffect(shadow)

    @pyqtProperty(float)
    def icon_size(self):
        return self._icon_size

    @icon_size.setter
    def icon_size(self, size):
        self._icon_size = size
        self.update()

    def enterEvent(self, event):
        self.animate_icon_size(30)

    def leaveEvent(self, event):
        self.animate_icon_size(25)

    def animate_icon_size(self, target_size):
        self.animation = QPropertyAnimation(self, b"icon_size")
        self.animation.setDuration(100)
        self.animation.setStartValue(self.icon_size)
        self.animation.setEndValue(target_size)
        self.animation.start()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)
        icon_rect = QRectF((self.width() - self._icon_size) / 2,
                           (self.height() - self._icon_size) / 2,
                           self._icon_size, self._icon_size)
        self.icon.paint(painter, icon_rect.toRect())

class CargarSeccionDialog(QDialog):
    def __init__(self, parent=None, ultima_salva_path=None):
        super().__init__(parent)
        self.ultima_salva_path = ultima_salva_path
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setModal(True)
        self.content_frame = QFrame(self) # Crear un QFrame para el contenido
        self.content_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.content_frame.setFrameShadow(QFrame.Shadow.Raised)
        self.content_frame.setStyleSheet("""
            QFrame {
                background-color: #2D2D2C;
                border-radius: 15px;
                border: 1px solid gray;
            }
        """)
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(15)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(0)
        self.shadow_effect.setColor(Qt.GlobalColor.white)
        self.content_frame.setGraphicsEffect(self.shadow_effect)
        content_layout = QVBoxLayout(self.content_frame)
        content_layout.setContentsMargins(15, 15, 15, 15)
        self.label = QLabel("PENDIENTE")
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label.setStyleSheet("color: white; font-size: 14px; font-weight: bold; border: none;")
        content_layout.addWidget(self.label)
        button_layout = QHBoxLayout()
        button_layout.setSpacing(5)
        content_layout.addLayout(button_layout)
        self.btn_cargar = AnimatedButton('Confirmar DIalogo.png', "Cargar conversión pendiente", self.cargar_pendiente)
        self.btn_explorar = AnimatedButton('Explorar.png', "Abrir explorador de archivos", self.abrir_explorador)
        self.btn_cancelar = AnimatedButton('Cerrar Dialogo.png', "Cancelar", self.reject)
        button_layout.addStretch()
        button_layout.addWidget(self.btn_cargar)
        button_layout.addWidget(self.btn_explorar)
        button_layout.addWidget(self.btn_cancelar)
        button_layout.addStretch()
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(self.content_frame)
        self.archivo_seleccionado = None

    def cargar_pendiente(self):
        current_dir = os.path.dirname(os.path.realpath(__file__))
        salva_dir = os.path.join(current_dir, 'SALVA')
        file_name = os.path.join(salva_dir, 'ultima_salva.json')
        if os.path.exists(file_name):
            self.archivo_seleccionado = file_name
            self.accept()
        else:
            print("No se encontró un archivo de conversión pendiente.")
            self.reject()
    def abrir_explorador(self):
        archivo, _ = QFileDialog.getOpenFileName(self, "Seleccionar archivo de conversión", "", "Archivos JSON (*.json)")
        if archivo:
            self.archivo_seleccionado = archivo
            self.accept()

def mostrar_dialogo_carga(parent=None, ultima_salva_path=None):
    print("Mostrando diálogo de carga")
    dialogo = CargarSeccionDialog(parent, ultima_salva_path)
    resultado = dialogo.exec()
    if resultado == QDialog.DialogCode.Accepted and dialogo.archivo_seleccionado:
        print(f"Archivo seleccionado en el diálogo: {dialogo.archivo_seleccionado}")
        return dialogo.archivo_seleccionado
    print("No se seleccionó ningún archivo en el diálogo")
    return None

def cargar_datos_conversion(archivo):
    print(f"Cargando datos de conversión desde: {archivo}")
    try:
        with open(archivo, 'r') as f:
            datos = json.load(f)
        print(f"Datos cargados: {datos}")
        return datos
    except Exception as e:
        print(f"Error al cargar el archivo de conversión: {e}")
        return None