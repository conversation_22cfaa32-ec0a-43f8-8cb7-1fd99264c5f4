from PyQt6.QtWidgets import QComboBox, QGraphicsDropShadowEffect
from PyQt6.QtGui import QPainter, QColor
from PyQt6.QtCore import QSize


class IconComboBox(QComboBox):
    """ComboBox personalizado con icono integrado"""
    
    def __init__(self, icon_func=None, combo_type="normal", parent=None):
        super().__init__(parent)
        self.icon_func = icon_func
        self.combo_type = combo_type
        self._setup_style()
        self._setup_shadow()
    
    def _setup_style(self):
        """Configura el estilo del ComboBox según su tipo"""
        if self.combo_type == "limit":
            # Estilo más pequeño para el ComboBox de límite
            style = """
                QComboBox {
                    border: none;
                    border-radius: 10px;
                    padding: 2px 6px 2px 22px;  /* Espacio para el icono */
                    background-color: rgba(40, 40, 40, 220);
                    color: white;
                    min-width: 25px;
                    max-width: 45px;
                    font-size: 10px;
                    font-weight: bold;
                    height: 20px;
                }
                QComboBox::drop-down {
                    border: none;
                    background-color: transparent;
                    width: 0px;
                }
                QComboBox::down-arrow {
                    image: none;
                    width: 0px;
                    height: 0px;
                }
                QComboBox::item {
                    padding: 4px 6px;
                    background-color: rgba(40, 40, 40, 220);
                    color: white;
                    border: none;
                }
                QComboBox::item:selected {
                    background-color: rgba(0, 120, 212, 200);
                    color: white;
                }
                QComboBox:hover {
                    background-color: rgba(60, 60, 60, 220);
                }
                QComboBox QAbstractItemView {
                    border: none;
                    border-radius: 6px;
                    background-color: rgba(40, 40, 40, 240);
                    selection-background-color: rgba(0, 120, 212, 200);
                    outline: none;
                }
            """
        else:
            # Estilo normal para otros ComboBox
            style = """
                QComboBox {
                    border: none;
                    border-radius: 12px;
                    padding: 2px 8px 2px 25px;  /* Espacio para el icono */
                    background-color: rgba(40, 40, 40, 220);
                    color: white;
                    min-width: 45px;
                    max-width: 120px;
                    font-size: 10px;
                    font-weight: bold;
                    height: 22px;
                }
                QComboBox::drop-down {
                    border: none;
                    background-color: transparent;
                    width: 0px;
                }
                QComboBox::down-arrow {
                    image: none;
                    width: 0px;
                    height: 0px;
                }
                QComboBox::item {
                    padding: 5px 8px;
                    background-color: rgba(40, 40, 40, 220);
                    color: white;
                    border: none;
                }
                QComboBox::item:selected {
                    background-color: rgba(0, 120, 212, 200);
                    color: white;
                }
                QComboBox:hover {
                    background-color: rgba(60, 60, 60, 220);
                }
                QComboBox QAbstractItemView {
                    border: none;
                    border-radius: 8px;
                    background-color: rgba(40, 40, 40, 240);
                    selection-background-color: rgba(0, 120, 212, 200);
                    outline: none;
                }
            """
        
        self.setStyleSheet(style)
    
    def _setup_shadow(self):
        """Configura el efecto de sombra"""
        shadow_effect = QGraphicsDropShadowEffect()
        shadow_effect.setBlurRadius(8)
        shadow_effect.setXOffset(0)
        shadow_effect.setYOffset(2)
        shadow_effect.setColor(QColor(0, 0, 0, 100))
        self.setGraphicsEffect(shadow_effect)
    
    def paintEvent(self, event):
        """Dibuja el ComboBox con el icono"""
        # Llamar al paintEvent original
        super().paintEvent(event)
        
        # Dibujar el icono si existe
        if self.icon_func:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # Obtener el icono
            icon = self.icon_func(14)  # Tamaño del icono
            
            # Calcular posición del icono
            if self.combo_type == "limit":
                # Posición para ComboBox pequeño
                icon_x = 4
                icon_size = 14
            else:
                # Posición para ComboBox normal
                icon_x = 5
                icon_size = 16
            
            icon_rect = self.rect()
            icon_rect.setLeft(icon_x)
            icon_rect.setWidth(icon_size)
            icon_rect.setTop((icon_rect.height() - icon_size) // 2)
            icon_rect.setHeight(icon_size)
            
            # Dibujar el icono
            icon.paint(painter, icon_rect)
            painter.end()


def create_destination_combobox(parent=None):
    """Crea el ComboBox de destino con icono de ruta"""
    from CREAR import icono_RUTA_CONVERSION
    
    combobox = IconComboBox(icono_RUTA_CONVERSION, "normal", parent)
    combobox.addItem("...")
    combobox.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
    return combobox


def create_conversion_limit_combobox(parent=None):
    """Crea el ComboBox de límite de conversiones con icono de cantidad"""
    from CREAR import icono_CANTIDAD_CONVERSION
    
    combobox = IconComboBox(icono_CANTIDAD_CONVERSION, "limit", parent)
    for i in range(1, 17):
        combobox.addItem(str(i))
    return combobox


def create_settings_combobox(parent=None):
    """Crea el ComboBox de configuraciones con icono de engranaje"""
    from CREAR import icono_CONFIGURACION
    
    combobox = IconComboBox(icono_CONFIGURACION, "normal", parent)
    return combobox


def create_standard_combobox(parent=None):
    """Crea un ComboBox estándar sin icono"""
    combobox = IconComboBox(None, "normal", parent)
    return combobox