import os, sys
import json
from PyQt6.QtWidgets import (QFileDialog, QApplication, QMessageBox, QListWidgetItem, 
                             QPushButton, QHBoxLayout, QVBoxLayout, QLabel, QDialog, 
                             QGraphicsDropShadowEffect, QFrame)
from PyQt6.QtCore import Qt, QSize, QEvent, QPropertyAnimation, pyqtProperty, QRectF
from PyQt6.QtGui import QIcon, QCursor, QColor, QPainter, QFont

def get_icon_path(icon_name):
    if getattr(sys, 'frozen', False):
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(os.path.abspath(__file__))
    icon_path = os.path.join(base_path, 'ICONOS', icon_name)
    if not os.path.exists(icon_path):
        print(f"Advertencia: No se pudo encontrar el icono: {icon_path}")
    return icon_path

class AnimatedButton(QPushButton):
    def __init__(self, icon_name, tooltip, click_handler=None, parent=None):
        super().__init__(parent)
        self.setStyleSheet("QPushButton {border: none; background: transparent;}")
        self._icon_size = 25
        self.setFixedSize(35, 35)
        icon_path = get_icon_path(icon_name)
        self.icon = QIcon(icon_path)
        self.setMouseTracking(True)
        self.setToolTip(tooltip)
        self.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        if click_handler:
            self.clicked.connect(click_handler)
        
        self.add_shadow_effect(Qt.GlobalColor.white)

    def add_shadow_effect(self, color):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(color)
        self.setGraphicsEffect(shadow)

    @pyqtProperty(float)
    def icon_size(self):
        return self._icon_size

    @icon_size.setter
    def icon_size(self, size):
        self._icon_size = size
        self.update()

    def enterEvent(self, event):
        self.animate_icon_size(30)

    def leaveEvent(self, event):
        self.animate_icon_size(25)

    def animate_icon_size(self, target_size):
        self.animation = QPropertyAnimation(self, b"icon_size")
        self.animation.setDuration(100)
        self.animation.setStartValue(self.icon_size)
        self.animation.setEndValue(target_size)
        self.animation.start()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)

        icon_rect = QRectF((self.width() - self._icon_size) / 2,
                           (self.height() - self._icon_size) / 2,
                           self._icon_size, self._icon_size)
        self.icon.paint(painter, icon_rect.toRect())

class CargarDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setModal(True)

        self.content_frame = QFrame(self)
        self.content_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.content_frame.setFrameShadow(QFrame.Shadow.Raised)
        self.content_frame.setStyleSheet("""
            QFrame {
                background-color: #2D2D2C;
                border-radius: 15px;
                border: 1px solid gray;
            }
        """)
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(15)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(0)
        self.shadow_effect.setColor(Qt.GlobalColor.white)
        self.content_frame.setGraphicsEffect(self.shadow_effect)

        content_layout = QVBoxLayout(self.content_frame)
        content_layout.setContentsMargins(15, 15, 15, 15)

        self.label = QLabel("CARGA AUTOMÁTICA")
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label.setStyleSheet("color: white; font-size: 14px; font-weight: bold; border: none;")
        content_layout.addWidget(self.label)

        button_layout = QHBoxLayout()
        button_layout.setSpacing(5)
        content_layout.addLayout(button_layout)

        self.btn_cargar = AnimatedButton('ACEPTAR-1.png', "Cargar última conversión", self.accept)
        self.btn_explorar = AnimatedButton('Explorar.png', "Seleccionar archivo manualmente", self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.btn_cargar)
        button_layout.addWidget(self.btn_explorar)
        button_layout.addStretch()

        main_layout = QVBoxLayout(self)
        main_layout.addWidget(self.content_frame)

def on_salvarButton_clicked(self):
    file_paths = [self.listWidget.item(i).data(Qt.ItemDataRole.UserRole) for i in range(self.listWidget.count())]
    file_name, _ = QFileDialog.getSaveFileName(
        self,
        "Guardar como",
        "",
        "JSON Files (*.json)",
    )
    if file_name:
        if not file_name.endswith('.json'):
            file_name += '.json'
        with open(file_name, 'w') as f:
            json.dump(file_paths, f)

def on_cargarButton_clicked(self):
    dialog = CargarDialog(self)
    
    # Configurar el estilo del diálogo según el modo actual
    is_light_mode = self.palette().color(self.backgroundRole()).lightness() > 128
    if is_light_mode:
        dialog.content_frame.setStyleSheet("""
            QFrame {
                background-color: #F0F0F0;
                border-radius: 15px;
                border: 1px solid #CCCCCC;
            }
        """)
        dialog.title_label.setStyleSheet("color: #333333; font-size: 16px; font-weight: bold;")
        dialog.message_label.setStyleSheet("color: #333333; font-size: 14px;")
    
    resultado = dialog.exec()

    if resultado == QDialog.DialogCode.Accepted:
        cargar_ultima_conversion(self)
    else:
        cargar_conversion_manual(self)

def cargar_ultima_conversion(self):
    current_dir = os.path.dirname(os.path.realpath(__file__))
    salva_dir = os.path.join(current_dir, 'SALVA')
    file_name = os.path.join(salva_dir, 'ultima_salva.json')
    cargar_conversion(self, file_name)

def cargar_conversion_manual(self):
    file_name, _ = QFileDialog.getOpenFileName(
        self,
        "Abrir",
        "",
        "JSON Files (*.json)",
    )
    if file_name:
        cargar_conversion(self, file_name)

def cargar_conversion(self, file_name):
    try:
        with open(file_name, 'r') as f:
            file_paths = json.load(f)
        for file_path in file_paths:
            if not any(self.listWidget.item(i).data(Qt.ItemDataRole.UserRole) == file_path for i in range(self.listWidget.count())):
                item = QListWidgetItem(os.path.basename(file_path))
                item.setData(Qt.ItemDataRole.UserRole, file_path)
                
                # Crear una fuente personalizada
                font = QFont()
                font.setPointSize(9)  # Texto más pequeño consistente
                font.setBold(True)
                
                # Aplicar la fuente al item
                item.setFont(font)
                
          
    except (FileNotFoundError, json.JSONDecodeError):
        print(f"No se pudo cargar el archivo: {file_name}")