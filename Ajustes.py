from PyQt6.QtWidgets import (<PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout, QComboBox, QColorDialog, QLabel, QPushButton, QInputDialog, QSizePolicy, QGroupBox, QFrame)
from PyQt6.QtGui import QIcon, QCursor, QPainter, QBrush, QColor, QMouseEvent, QFontDatabase
from PyQt6.QtCore import pyqtSignal, QSize, Qt, QPoint, QEvent, QTimer
from PyQt6.QtWidgets import QTabWidget, QMessageBox
import json
import sys
import os
from ctypes import windll, byref, sizeof, c_int
from APARIENCIA import ACCENT_POLICY, WINDOWCOMPOSITIONATTRIBDATA, apply_acrylic_effect, apply_acrylic_and_rounded
class ButtonContainer(QFrame):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(40, 40)
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
class Ajustes(QWidget):
    parametros_actualizados = pyqtSignal(dict)
    def __init__(self, parent=None):
        super(Ajustes, self).__init__(parent)
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        # Inicializar windowModeComboBox antes de cargar configuración
        self.windowModeComboBox = None
        self.initUI()
        self.oldPos = self.pos()
        self.dragging = False
        # Aplicar efecto acrílico inmediatamente después de crear la ventana
        QTimer.singleShot(0, self.aplicar_efecto_acrilico)
        QTimer.singleShot(100, self.cargar_configuracion)
        self.aplicar_estilo_oscuro()
    def initUI(self):
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setFixedSize(300, 500)
        self.setWindowOpacity(1)
        
        # Aplicar efecto acrílico al iniciar
        hwnd = self.winId().__int__()
        apply_acrylic_and_rounded(hwnd, mode='dark')
        
        layout = QVBoxLayout(self)
        groupBox = QGroupBox("CONVER-ZETA")
        groupBoxLayout = QVBoxLayout()
        self.scaleComboBox = QComboBox()
        self.scaleComboBox.addItems(['scale=624:352', 'scale=640:480', 'scale=640:360', 'scale=320:240', 'scale=352:240'])
        self.framerateComboBox = QComboBox()
        self.aspectRatioComboBox = QComboBox()
        self.aspectRatioComboBox.addItems(['16:9', '4:3'])
        self.framerateComboBox.addItems(['24', '29.97', '30', '60'])
        self.vcodecComboBox = QComboBox()
        self.vcodecComboBox.addItems(['mpeg1video', 'h264', 'libx264', 'mpeg4', 'libxvid'])
        self.bitrateComboBox = QComboBox()
        self.bitrateComboBox.addItems(['1150k', '1500k', '2000k'])
        self.acodecComboBox = QComboBox()
        self.acodecComboBox.addItems(['mp2', 'aac', 'ac3','mp3', 'libmp3lame'])
        self.audioBitrateComboBox = QComboBox()
        self.audioBitrateComboBox.addItems(['128k', '192k', '256k'])
        self.audioRateComboBox = QComboBox()
        self.audioRateComboBox.addItems(['44100', '48000'])
        self.audioChannelsComboBox = QComboBox()
        self.audioChannelsComboBox.addItems(['2', '5.1', '7.1'])
        self.extensionComboBox = QComboBox()
        self.extensionComboBox.addItems(['.mpg', '.avi', '.mp4'])
        scaleLayout = QHBoxLayout()
        scaleLayout.addWidget(QLabel('Scale:'))
        scaleLayout.addWidget(self.scaleComboBox)
        aspectRatioLayout = QHBoxLayout()
        aspectRatioLayout.addWidget(QLabel('Relación de Aspecto:'))
        aspectRatioLayout.addWidget(self.aspectRatioComboBox)
        framerateLayout = QHBoxLayout()
        framerateLayout.addWidget(QLabel('Frame Rate:'))
        framerateLayout.addWidget(self.framerateComboBox)
        vcodecLayout = QHBoxLayout()
        vcodecLayout.addWidget(QLabel('Video Codec:'))
        vcodecLayout.addWidget(self.vcodecComboBox)
        bitrateLayout = QHBoxLayout()
        bitrateLayout.addWidget(QLabel('Bitrate:'))
        bitrateLayout.addWidget(self.bitrateComboBox)
        acodecLayout = QHBoxLayout()
        acodecLayout.addWidget(QLabel('Audio Codec:'))
        acodecLayout.addWidget(self.acodecComboBox)
        audioBitrateLayout = QHBoxLayout()
        audioBitrateLayout.addWidget(QLabel('Audio Bitrate:'))
        audioBitrateLayout.addWidget(self.audioBitrateComboBox)
        audioRateLayout = QHBoxLayout()
        audioRateLayout.addWidget(QLabel('Audio Sample Rate:'))
        audioRateLayout.addWidget(self.audioRateComboBox)
        audioChannelsLayout = QHBoxLayout()
        audioChannelsLayout.addWidget(QLabel('Audio Channels:'))
        audioChannelsLayout.addWidget(self.audioChannelsComboBox)
        extensionLayout = QHBoxLayout()
        extensionLayout.addWidget(QLabel('Extensión:'))
        extensionLayout.addWidget(self.extensionComboBox)
        
        subtitleSizeLayout = QHBoxLayout()
        subtitleSizeLayout.addWidget(QLabel('Tamaño Subtítulo:'))
        self.subtitleSizeComboBox = QComboBox()
        self.subtitleSizeComboBox.addItems([str(i) for i in range(1, 41)])
        subtitleSizeLayout.addWidget(self.subtitleSizeComboBox)

        subtitleColorLayout = QHBoxLayout()
        subtitleColorLayout.addWidget(QLabel('Color Subtítulo:'))
        self.subtitleColorButton = QPushButton('Seleccionar Color')
        self.subtitleColorButton.clicked.connect(self.selectColor)
        subtitleColorLayout.addWidget(self.subtitleColorButton)

        subtitleFontLayout = QHBoxLayout()
        subtitleFontLayout.addWidget(QLabel('Fuente Subtítulo:'))
        self.subtitleFontComboBox = QComboBox()
        self.subtitleFontComboBox.addItems(QFontDatabase.families())
        subtitleFontLayout.addWidget(self.subtitleFontComboBox)

        groupBoxLayout.addLayout(scaleLayout)
        groupBoxLayout.addLayout(aspectRatioLayout)
        groupBoxLayout.addLayout(framerateLayout)
        groupBoxLayout.addLayout(vcodecLayout)
        groupBoxLayout.addLayout(bitrateLayout)
        groupBoxLayout.addLayout(acodecLayout)
        groupBoxLayout.addLayout(audioBitrateLayout)
        groupBoxLayout.addLayout(audioRateLayout)
        groupBoxLayout.addLayout(audioChannelsLayout)
        groupBoxLayout.addLayout(extensionLayout)
        groupBoxLayout.addLayout(subtitleSizeLayout)
        groupBoxLayout.addLayout(subtitleColorLayout)
        groupBoxLayout.addLayout(subtitleFontLayout)
        
        buttonsLayout = QHBoxLayout()
        buttonsLayout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        buttonsLayout.setSpacing(10)  # Espacio entre los botones

        # Usar botones SVG animados
        from botones import crear_boton_confirmar_svg, crear_boton_cerrar_svg
        self.applyButton = crear_boton_confirmar_svg(self)
        self.applyButton.clicked.connect(self.applySettings)
        self.closeButton = crear_boton_cerrar_svg(self)
        self.closeButton.clicked.connect(self.close)

        buttonsLayout.addWidget(self.applyButton)
        buttonsLayout.addWidget(self.closeButton)
        groupBoxLayout.addLayout(buttonsLayout)

        groupBox.setLayout(groupBoxLayout)
        layout.addWidget(groupBox)
        self.setLayout(layout)
        # Crear el widget de pestañas
        tabWidget = QTabWidget(self)
        tabAjustesActuales = QWidget()
        tabNuevosAjustes = QWidget()

        # Configurar el layout para la pestaña de ajustes actuales
        tabAjustesActuales.setLayout(groupBoxLayout)
        tabAjustesActuales.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)

        # Configurar el layout para la pestaña de configuración
        newSettingsLayout = QVBoxLayout()
        self.configureNewSettingsLayout(newSettingsLayout)
        tabNuevosAjustes.setLayout(newSettingsLayout)
        tabNuevosAjustes.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)

        tabWidget.addTab(tabAjustesActuales, "AJUSTES")
        tabWidget.addTab(tabNuevosAjustes, "CONFIGURACION")
        layout.addWidget(tabWidget)
        tabWidget.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        tabWidget.setStyleSheet("QTabWidget::pane { border: 0; }")

    def aplicar_estilo_oscuro(self):
        estilo_oscuro = """
        QWidget {
            background-color: #333333;
            color: white;
        }
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #333333;
        }
        QTabWidget::tab-bar {
            left: 5px;
        }
        QTabBar::tab {
            background-color: #444444;
            color: white;
            padding: 5px;
            border: 1px solid #555555;
            border-bottom-color: #333333;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        QTabBar::tab:selected, QTabBar::tab:hover {
            background-color: #333333;
        }
        QTabBar::tab:selected {
            border-color: #555555;
            border-bottom-color: #333333;
        }
        QLabel {
            color: white;
        }
        QPushButton {
            background-color: #444444;
            color: white;
            border: 1px solid #555555;
            padding: 5px;
        }
        QPushButton:hover {
            background-color: #555555;
        }
        """
        self.setStyleSheet(estilo_oscuro)

        # Aplicar estilo solo a los ComboBox específicos de la pestaña de configuración
        config_combos = [self.windowModeComboBox]
        for combo in config_combos:
            if combo:
                combo.setStyleSheet("""
                    QComboBox {
                        background-color: #444444;
                        color: white;
                        border: 1px solid #555555;
                        padding: 1px 18px 1px 3px;
                        min-width: 6em;
                    }
                    QComboBox::drop-down {
                        subcontrol-origin: padding;
                        subcontrol-position: top right;
                        width: 15px;
                        border-left-width: 1px;
                        border-left-color: #555555;
                        border-left-style: solid;
                    }
                    QComboBox::down-arrow {
                        image: url(iconos/flecha_blanca.png);
                    }
                    QComboBox QAbstractItemView {
                        border: 1px solid #555555;
                        background-color: #444444;
                        color: white;
                        selection-background-color: #555555;
                    }
                """)
    def configureNewSettingsLayout(self, layout):
        # Configuración para la pestaña CONFIGURACION
        windowModeLayout = QHBoxLayout()
        windowModeLabel = QLabel("MODO VENTANA")
        self.windowModeComboBox = QComboBox()
        self.windowModeComboBox.addItems(["Modo Claro", "Modo Oscuro"])
        self.windowModeComboBox.setCurrentIndex(1)  # Por defecto en modo oscuro
        self.windowModeComboBox.currentIndexChanged.connect(self.changeWindowMode)
        
        windowModeLayout.addWidget(windowModeLabel)
        windowModeLayout.addWidget(self.windowModeComboBox)
        
        # Añadir botón para seleccionar el color de la barra de progreso
        progressBarColorLayout = QHBoxLayout()
        progressBarColorLabel = QLabel("Color de la barra de progreso:")
        self.progressBarColorButton = QPushButton()
        self.progressBarColorButton.setFixedSize(30, 30)
        self.progressBarColorButton.clicked.connect(self.selectProgressBarColor)
        self.progressBarColorButton.setStyleSheet(f'background-color: #0078D7;')  # Color inicial
        
        progressBarColorLayout.addWidget(progressBarColorLabel)
        progressBarColorLayout.addWidget(self.progressBarColorButton)
        
        layout.addLayout(windowModeLayout)
        layout.addLayout(progressBarColorLayout)
        layout.addStretch()  # Añadir un espacio flexible al final para empujar todo hacia arriba

    def selectProgressBarColor(self):
        color = QColorDialog.getColor()
        if color.isValid():
            self.progressBarColorButton.setStyleSheet(f'background-color: {color.name()};')
            self.progressBarColor = color.name()
            print(f"Nuevo color de barra de progreso seleccionado: {self.progressBarColor}")
            self.parametros_actualizados.emit({'progress_bar_color': self.progressBarColor})
            self.guardarConfiguracion(progress_bar_color=self.progressBarColor)

    def cargar_configuracion(self):
        if self.windowModeComboBox is None:
            return

        if getattr(sys, 'frozen', False):
            # Si es una aplicación compilada
            base_path = sys._MEIPASS
        else:
            # Si es el script en desarrollo
            base_path = os.path.dirname(os.path.abspath(__file__))

        config_path = os.path.join(base_path, 'config.txt')
        print(f"Ruta del archivo de configuración: {config_path}")
        
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as config_file:
                    config = json.load(config_file)
                    mode = config.get('modo', 'oscuro')
                    print(f"Modo leído del archivo: {mode}")
                    if mode == 'claro':
                        self.windowModeComboBox.setCurrentIndex(0)
                        self.apply_acrylic('light')
                    else:
                        self.windowModeComboBox.setCurrentIndex(1)
                        self.apply_acrylic('dark')
                    
                    # Cargar el color de la barra de progreso si existe
                    progress_bar_color = config.get('progress_bar_color')
                    if progress_bar_color:
                        self.progressBarColorButton.setStyleSheet(f'background-color: {progress_bar_color};')
                        self.progressBarColor = progress_bar_color
            except json.JSONDecodeError:
                print("El archivo de configuración no es un JSON válido. Se usará la configuración por defecto.")
                self.windowModeComboBox.setCurrentIndex(1)
                self.apply_acrylic('dark')
        else:
            print("El archivo de configuración no existe")
            self.windowModeComboBox.setCurrentIndex(1)
            self.apply_acrylic('dark')

    def apply_acrylic(self, mode):
        """Método legacy para mantener compatibilidad"""
        try:
            hwnd = self.winId().__int__()
            apply_acrylic_and_rounded(hwnd, mode)
            print(f"Efecto acrílico aplicado en modo: {mode}")
            return True
        except Exception as e:
            print(f"Error al aplicar efecto acrílico: {e}")
            return False

    def aplicar_efecto_acrilico(self):
        """Aplica el efecto acrílico a la ventana"""
        try:
            hwnd = self.winId().__int__()
            # Usar la nueva función que maneja tanto Windows 10 como Windows 11
            mode = 'light' if self.windowModeComboBox and self.windowModeComboBox.currentIndex() == 0 else 'dark'
            
            # Importar aquí para evitar problemas de importación circular
            from APARIENCIA import apply_acrylic_and_rounded
            
            result = apply_acrylic_and_rounded(hwnd, mode)
            if result:
                print(f"Efecto acrílico aplicado correctamente en modo: {mode}")
            else:
                print(f"No se pudo aplicar el efecto acrílico en modo: {mode}")
        except Exception as e:
            print(f"Error al aplicar efecto acrílico: {e}")

    def showEvent(self, event):
        """Se ejecuta cada vez que la ventana se muestra"""
        super().showEvent(event)
        # Volver a aplicar el efecto acrílico cuando la ventana se muestra
        QTimer.singleShot(10, self.aplicar_efecto_acrilico)

    def changeWindowMode(self, index):
        """Cambia el modo de la ventana (claro/oscuro)"""
        mode = 'light' if index == 0 else 'dark'
        self.apply_acrylic(mode)
        self.parametros_actualizados.emit({'modo': mode})
        self.guardarConfiguracion(mode)
        if self.parent():
            try:
                parent_hwnd = self.parent().winId().__int__()
                from APARIENCIA import apply_acrylic_and_rounded
                apply_acrylic_and_rounded(parent_hwnd, mode)
                print(f"Efecto acrílico aplicado a ventana principal en modo: {mode}")
            except Exception as e:
                print(f"Error al aplicar efecto acrílico a ventana principal: {e}")

    def guardarConfiguracion(self, modo=None, progress_bar_color=None):
        if getattr(sys, 'frozen', False):
            base_path = os.path.dirname(sys.executable)
        else:
            base_path = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(base_path, 'config.txt')
        config = {}
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as config_file:
                    config = json.load(config_file)
            except json.JSONDecodeError:
                print("El archivo de configuración está vacío o no es un JSON válido. Se creará uno nuevo.")
        if modo is not None:
            config['modo'] = 'claro' if modo == 'light' else 'oscuro'
        if progress_bar_color is not None:
            config['progress_bar_color'] = progress_bar_color
        try:
            with open(config_path, 'w') as config_file:
                json.dump(config, config_file, indent=4)
            print(f"Configuración guardada exitosamente en {config_path}")
        except Exception as e:
            print(f"Error al guardar la configuración: {e}")

    def selectColor(self):
        color = QColorDialog.getColor()
        if color.isValid():
            self.subtitleColorButton.setStyleSheet(f'background-color: {color.name()};')
            self.subtitleColor = f"&H{color.blue():02X}{color.green():02X}{color.red():02X}&" # Guardar el color en el formato &HBBGGRR&

    def create_button(self, icon_name, tooltip, on_click):
        container = ButtonContainer(self)
        button = QPushButton(container)
        icon_path = os.path.join(os.path.dirname(__file__), 'iconos', icon_name)
        button.setIcon(QIcon(icon_path))
        button.setIconSize(QSize(35, 35))
        button.setCursor(Qt.CursorShape.PointingHandCursor)
        button.setFlat(True)
        button.setToolTip(tooltip)
        button.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        button.clicked.connect(on_click)
        button.setStyleSheet("""
            QPushButton {
                border: none;
                background-color: transparent;
            }
            QPushButton:hover {
                background-color: transparent;
            }
            QPushButton:pressed {
                background-color: transparent;
            }
        """)
        container.layout.addWidget(button)

        def change_icon_size(event):
            if event.type() == QEvent.Type.Enter:
                button.setIconSize(QSize(40, 40))
            elif event.type() == QEvent.Type.Leave:
                button.setIconSize(QSize(35, 35))
        button.enterEvent = change_icon_size
        button.leaveEvent = change_icon_size
        return container

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setBrush(QColor(255, 255, 255, 1))  # Color blanco casi transparente
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRect(self.rect())

    def set_rounded_corners(self, hwnd):
        DWMWA_WINDOW_CORNER_PREFERENCE = 33
        DWMWCP_ROUND = 2
        windll.dwmapi.DwmSetWindowAttribute(hwnd, DWMWA_WINDOW_CORNER_PREFERENCE, byref(c_int(DWMWCP_ROUND)), sizeof(c_int))
    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = True
            self.oldPos = event.globalPosition().toPoint()
    def mouseMoveEvent(self, event: QMouseEvent):
        if self.dragging and event.buttons() & Qt.MouseButton.LeftButton:
            delta = event.globalPosition().toPoint() - self.oldPos
            self.move(self.x() + delta.x(), self.y() + delta.y())
            self.oldPos = event.globalPosition().toPoint()
    def mouseReleaseEvent(self, event: QMouseEvent):
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False
    def applySettings(self):
        nuevos_parametros = {
            'scale': self.scaleComboBox.currentText(),
            'aspect_ratio': self.aspectRatioComboBox.currentText(),
            'framerate': self.framerateComboBox.currentText(),
            'vcodec': self.vcodecComboBox.currentText(),
            'bitrate': self.bitrateComboBox.currentText(),
            'acodec': self.acodecComboBox.currentText(),
            'audio_bitrate': self.audioBitrateComboBox.currentText(),
            'audio_rate': self.audioRateComboBox.currentText(),
            'audio_channels': self.audioChannelsComboBox.currentText(),
            'extension': self.extensionComboBox.currentText(),
            'subtitle_size': self.subtitleSizeComboBox.currentText(),
            'subtitle_color': getattr(self, 'subtitleColor', '&H00FFFF&'),
            'subtitle_font': self.subtitleFontComboBox.currentText()
        }
        nombre_ajuste, ok = QInputDialog.getText(self, 'Guardar Ajuste', 'Ingrese el nombre del ajuste:')
        if ok and nombre_ajuste:
            if getattr(sys, 'frozen', False):
                # Si es una aplicación compilada
                base_path = os.path.dirname(sys.executable)
            else:
                # Si es el script en desarrollo
                base_path = os.path.dirname(os.path.abspath(__file__))
            
            config_path = os.path.join(base_path, f'{nombre_ajuste}.json')
            try:
                with open(config_path, 'w') as config_file:
                    json.dump(nuevos_parametros, config_file, indent=4)
                print(f"Ajustes guardados exitosamente en {config_path}")
                self.parametros_actualizados.emit(nuevos_parametros)
                self.parent().parametros_conversion.update(nuevos_parametros)
                self.parent().load_settings_names()
            except Exception as e:
                print(f"Error al guardar los ajustes: {e}")
                QMessageBox.warning(self, "Error", f"No se pudieron guardar los ajustes: {str(e)}")

if __name__ == '__main__':
    app = QApplication([])
    ventana = Ajustes()
    ventana.show()
    sys.exit(app.exec())