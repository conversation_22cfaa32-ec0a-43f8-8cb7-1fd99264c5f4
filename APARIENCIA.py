import ctypes
from PyQt6.QtWidgets import QMain<PERSON><PERSON>ow, QVBoxLayout, QComboBox, QWidget
import sys

class ACCENT_POLICY(ctypes.Structure):
    _fields_ = [("AccentState", ctypes.c_int),
                ("AccentFlags", ctypes.c_int),
                ("GradientColor", ctypes.c_int),
                ("AnimationId", ctypes.c_int)]

class WINDOWCOMPOSITIONATTRIBDATA(ctypes.Structure):
    _fields_ = [("Attribute", ctypes.c_int),
                ("Data", ctypes.POINTER(ctypes.c_int)),
                ("SizeOfData", ctypes.c_size_t)]

ACCENT_ENABLE_ACRYLICBLURBEHIND = 3
ACCENT_ENABLE_BLURBEHIND = 2  # Para Windows 10
WCA_ACCENT_POLICY = 19
DWMWA_WINDOW_CORNER_PREFERENCE = 33
DWMWCP_ROUND = 2
ACCENT_ENABLE_FLUENT = 4  # Efecto Fluent Design de Windows 10

def is_windows_11_or_greater():
    """Detecta si el sistema es Windows 11 o superior"""
    try:
        version = sys.getwindowsversion()
        return version.build >= 22000
    except:
        return False

def apply_acrylic_effect(hwnd, mode='dark'):
    """Función legacy para mantener compatibilidad"""
    return apply_acrylic_and_rounded(hwnd, mode)

def apply_acrylic_and_rounded(hwnd, mode='dark', is_tooltip=False):
    """Aplica efecto acrílico y bordes redondeados a una ventana"""
    try:
        # Configurar el efecto acrílico
        accent = ACCENT_POLICY()
        
        # Usar efecto diferente según la versión de Windows y si es tooltip
        if is_windows_11_or_greater():
            accent.AccentState = ACCENT_ENABLE_ACRYLICBLURBEHIND
            accent.GradientColor = 0x01000000 if mode == 'dark' else 0x01FFFFFF
            accent.AccentFlags = 0x20 | 0x40 | 0x80 | 0x100
        else:
            # Usar el efecto Fluent Design para tooltips en Windows 10
            if is_tooltip:
                accent.AccentState = ACCENT_ENABLE_FLUENT
                # Color con 20% de opacidad para tooltips
                accent.GradientColor = 0x33000000 if mode == 'dark' else 0x33FFFFFF
            else:
                # Para ventanas normales en Windows 10, usar BLURBEHIND
                accent.AccentState = ACCENT_ENABLE_BLURBEHIND
                # Ajustar la opacidad para Windows 10
                accent.GradientColor = 0x50000000 if mode == 'dark' else 0x50FFFFFF

        data = WINDOWCOMPOSITIONATTRIBDATA()
        data.Attribute = WCA_ACCENT_POLICY
        data.SizeOfData = ctypes.sizeof(accent)
        data.Data = ctypes.cast(ctypes.pointer(accent), ctypes.POINTER(ctypes.c_int))

        # Aplicar efecto acrílico
        ctypes.windll.user32.SetWindowCompositionAttribute(hwnd, ctypes.byref(data))

        # Aplicar bordes redondeados solo si no es un tooltip
        if not is_tooltip:
            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_WINDOW_CORNER_PREFERENCE,
                ctypes.byref(ctypes.c_int(DWMWCP_ROUND)),
                ctypes.sizeof(ctypes.c_int)
            )
        
        return True
    except Exception as e:
        print(f"Error al aplicar efectos de ventana: {e}")
        return False