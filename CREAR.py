from PyQt6.QtWidgets import QPushButton, QLabel, QGraphicsDropShadowEffect, QWidget, QVBoxLayout
from PyQt6.QtGui import QPainter, QColor, QLinearGradient, QPen, QBrush, QPainterPath, QPolygon, QRadialGradient, QIcon, QPixmap
from PyQt6.QtCore import Qt, QPropertyAnimation, pyqtProperty, QPoint, QTimer, QRect, QSize, QEasingCurve, QRectF, QPointF, QEvent
import math

class CustomToolTip(QLabel):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setWindowFlags(Qt.WindowType.ToolTip | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
        self.setStyleSheet("""
            QLabel {
                color: white;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
            }
        """)
        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(150)
        self.opacity_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.scale_animation = QPropertyAnimation(self, b"geometry")
        self.scale_animation.setDuration(150)
        self.scale_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
    def paintEvent(self, event):
        if self.isVisible():
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
            path = QPainterPath()
            rect = QRectF(self.rect()).adjusted(1, 1, -1, -1)
            radius = min(rect.height(), rect.width()) * 0.47
            path.addRoundedRect(rect, radius, radius)
            painter.setPen(Qt.PenStyle.NoPen)
            for i in range(2):
                shadow_path = QPainterPath()
                shadow_rect = rect.adjusted(-i, -i, i, i)
                shadow_path.addRoundedRect(shadow_rect, radius, radius)
                painter.setBrush(QColor(0, 0, 0, 15 - i * 5))
                painter.drawPath(shadow_path)
            gradient = QLinearGradient(0, 0, 0, self.height())
            gradient.setColorAt(0.0, QColor(65, 65, 65, 250))
            gradient.setColorAt(0.5, QColor(55, 55, 55, 250))
            gradient.setColorAt(1.0, QColor(50, 50, 50, 250))
            painter.setBrush(gradient)
            painter.drawPath(path)
            pen = QPen()
            pen.setColor(QColor(255, 255, 255, 30))
            pen.setWidth(1)
            pen.setStyle(Qt.PenStyle.SolidLine)
            painter.setPen(pen)
            painter.drawPath(path)
            super().paintEvent(event)
        
    def hideEvent(self, event):
        self.opacity_animation.stop()
        self.scale_animation.stop()
        super().hideEvent(event)

    def show_tooltip(self, pos):
        self.adjustSize()
        final_pos = pos + QPoint(-self.width() // 2, 35)
        self.move(final_pos)
        self.show()

class CustomBaseButton(QPushButton):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(32, 32)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self._scale = 1.0
        self.setAttribute(Qt.WidgetAttribute.WA_Hover)
        self.setMouseTracking(True)
        
        # Efecto de sombra común
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(8)
        self.shadow.setXOffset(0)
        self.shadow.setYOffset(0)
        self.shadow.setColor(QColor(0, 0, 0, 160))
        self.setGraphicsEffect(self.shadow)
        
        # Animación común
        self._animation = QPropertyAnimation(self, b"scale", self)
        self._animation.setDuration(100)
        
        # Tooltip común
        self.tooltip = CustomToolTip("")
        self.tooltip_timer = QTimer(self)
        self.tooltip_timer.setSingleShot(True)
        self.tooltip_timer.timeout.connect(self.show_tooltip)
        self.setStyleSheet("QPushButton { border: none; background: transparent; }")
        self.setEnabled(True)

    @pyqtProperty(float)
    def scale(self):
        return self._scale

    @scale.setter
    def scale(self, value):
        self._scale = value
        self.update()

    def enterEvent(self, event):
        self._animation.setStartValue(1.0)
        self._animation.setEndValue(1.08)  # Aumentado de 1.05 a 1.08
        self._animation.start()
        if self.tooltip.text():
            self.tooltip_timer.start(100)

    def leaveEvent(self, event):
        self._animation.setStartValue(1.08)  # Aumentado de 1.05 a 1.08
        self._animation.setEndValue(1.0)
        self._animation.start()
        self.tooltip_timer.stop()
        if self.tooltip and self.tooltip.isVisible():
            self.tooltip.hide()

    def hideTooltip(self):
        self.tooltip_timer.stop()
        if self.tooltip and self.tooltip.isVisible():
            self.tooltip.hide()

    def show_tooltip(self):
        if not self.tooltip.isVisible() and self.tooltip.text():
            pos = self.mapToGlobal(QPoint(0, 0))
            self.tooltip.show_tooltip(pos + QPoint(self.width()//2, 0))

    def setToolTip(self, text):
        self.tooltip.setText(text)

    def drawButtonBase(self, painter, gradient_colors):
        """Método común para dibujar el botón base con aro exterior y sombra 3D"""
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        button_size = 26  # Aumentado de 22 a 26
        for i in range(3):
            shadow_path = QPainterPath()
            shadow_rect = QRectF(0, 0, button_size, button_size).adjusted(i, i, -i, -i)
            shadow_path.addEllipse(shadow_rect)
            if i == 0:
                shadow_color = QColor(0, 0, 0, 40)
            elif i == 1:
                shadow_color = QColor(0, 0, 0, 25)
            else:
                shadow_color = QColor(0, 0, 0, 15)
            painter.setPen(QPen(shadow_color, 1.0))
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawPath(shadow_path)
        
        # Círculo exterior (aro)
        outer_gradient = QLinearGradient(0, 0, 0, button_size)
        outer_gradient.setColorAt(0.0, QColor(255, 255, 255, 60))
        outer_gradient.setColorAt(0.5, QColor(255, 255, 255, 30))
        outer_gradient.setColorAt(1.0, QColor(255, 255, 255, 10))
        
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(QBrush(outer_gradient))
        painter.drawEllipse(1, 1, button_size-2, button_size-2)
        
        # Brillo superior sutil
        highlight = QLinearGradient(0, 2, 0, button_size * 0.5)
        highlight.setColorAt(0.0, QColor(255, 255, 255, 30))
        highlight.setColorAt(1.0, QColor(255, 255, 255, 0))
        painter.setBrush(QBrush(highlight))
        painter.drawEllipse(2, 2, button_size-4, button_size-4)

    def drawFloatingShadow(self, painter, center_x, center_y):
        """Método común para dibujar sombra flotante"""
        painter.setPen(Qt.PenStyle.NoPen)
        shadow_gradient = QRadialGradient(center_x, center_y, 8)
        shadow_gradient.setColorAt(0, QColor(0, 0, 0, 50))
        shadow_gradient.setColorAt(1, QColor(0, 0, 0, 0))
        painter.setBrush(shadow_gradient)
        painter.drawEllipse(QPoint(center_x + 1, center_y + 1), 8, 8)

    def paintEvent(self, event):
        """Template method para el pintado del botón"""
        if not self.isVisible():
            return
        painter = QPainter(self)
        if not painter.isActive():
            return
        try:
            button_size = 26  # Aumentado de 22 a 26
            margin = (self.width() - button_size) / 2
            
            painter.translate(self.width()/2, self.height()/2)
            painter.scale(self._scale, self._scale)
            painter.translate(-self.width()/2, -self.height()/2)
            
            painter.translate(margin, margin)
            
            self.drawButtonBase(painter, self.getGradientColors())
            self.drawSymbol(painter)
        finally:
            painter.end()
    
    def getGradientColors(self):
        """Método base para obtener los colores del gradiente"""
        # Color por defecto gris en caso de que una clase hija no implemente sus propios colores
        return {
            0.0: QColor(158, 158, 158),  # Gris claro
            0.4: QColor(117, 117, 117),  # Gris medio
            0.6: QColor(97, 97, 97),     # Gris medio-oscuro
            1.0: QColor(66, 66, 66)      # Gris oscuro
        }
    
    def drawSymbol(self, painter):
        """Método abstracto para dibujar el símbolo específico"""
        raise NotImplementedError

class ButtonColors:
    """Clase para centralizar los colores de los botones"""
    @staticmethod
    def get_colors(mode="normal", button_type="close"):
        colors = {
            "close": {
                "normal": {
                    0.0: QColor(255, 82, 82),   # Rojo claro
                    0.4: QColor(244, 67, 54),   # Rojo medio
                    0.6: QColor(229, 57, 53),   # Rojo medio-oscuro
                    1.0: QColor(211, 47, 47)    # Rojo oscuro
                },
            },
            "maximize": {
                "normal": {
                    0.0: QColor(66, 165, 245),  # Azul claro
                    0.4: QColor(33, 150, 243),  # Azul medio
                    0.6: QColor(30, 136, 229),  # Azul medio-oscuro
                    1.0: QColor(25, 118, 210)   # Azul oscuro
                },
            },
            "minimize": {
                "normal": {
                    0.0: QColor(129, 199, 132), # Verde claro
                    0.4: QColor(76, 175, 80),   # Verde medio
                    0.6: QColor(67, 160, 71),   # Verde medio-oscuro
                    1.0: QColor(56, 142, 60)    # Verde oscuro
                },
            }
        }
        
        # Colores comunes para modos comparison y copying
        comparison_colors = {
            0.0: QColor(66, 165, 245),  # Azul claro
            0.4: QColor(33, 150, 243),  # Azul medio
            0.6: QColor(30, 136, 229),  # Azul medio-oscuro
            1.0: QColor(25, 118, 210)   # Azul oscuro
        }
        
        copying_colors = {
            0.0: QColor(129, 199, 132), # Verde claro
            0.4: QColor(76, 175, 80),   # Verde medio
            0.6: QColor(67, 160, 71),   # Verde medio-oscuro
            1.0: QColor(56, 142, 60)    # Verde oscuro
        }

        if mode == "comparison":
            return comparison_colors
        elif mode == "copying":
            return copying_colors
        else:
            return colors.get(button_type, {}).get("normal", colors["close"]["normal"])

class WindowControlButton(CustomBaseButton):
    """Clase base para botones de control de ventana"""
    def __init__(self, tooltip="", parent=None):
        super().__init__(parent)
        self.tooltip_text = tooltip
        self.setFixedSize(28, 28)  # Más grandes para mejor visibilidad
        self.current_mode = "normal"
        self.clicked.connect(self.play_button_sound)
    
    def enterEvent(self, event):
        """Mostrar tooltip personalizado al entrar"""
        super().enterEvent(event)
        if self.tooltip_text:
            from TOOLTIP_APARIENCIA import showTooltipAtWidget
            showTooltipAtWidget(self.tooltip_text, self)
    
    def leaveEvent(self, event):
        """Ocultar tooltip al salir"""
        super().leaveEvent(event)
        from TOOLTIP_APARIENCIA import hideTooltip
        hideTooltip()
    def set_mode(self, mode):
        self.current_mode = mode
        self.update()
    def play_button_sound(self):
        """Reproduce el sonido de botón cuando se hace clic"""
        try:
            from SONIDOS import sound_manager
            sound_manager.play_sound('button')
        except Exception as e:
            print(f"Error al reproducir sonido de botón: {e}")
    def drawButtonBase(self, painter, gradient_colors):
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        button_size = self.width() - 4
        
        painter.translate(self.width()/2, self.height()/2)
        painter.scale(self._scale, self._scale)
        painter.translate(-self.width()/2, -self.height()/2)
        
        gradient = QLinearGradient(0, 0, 0, self.height())
        for position, color in gradient_colors.items():
            gradient.setColorAt(position, color)
        
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(QBrush(gradient))
        painter.drawEllipse(2, 2, button_size, button_size)

    def paintEvent(self, event):
        if not self.isVisible():
            return
        painter = QPainter(self)
        if not painter.isActive():
            return
        try:
            painter.translate(self.width()/2, self.height()/2)
            painter.scale(self._scale, self._scale)
            painter.translate(-self.width()/2, -self.height()/2)
            self.drawButtonBase(painter, self.getGradientColors())
            self.drawSymbol(painter)
        finally:
            painter.end()

class CustomCloseButton(WindowControlButton):
    def __init__(self, parent=None):
        super().__init__("Cerrar", parent)
        self.current_mode = "normal"
        
    def getGradientColors(self):
        return ButtonColors.get_colors(self.current_mode, "close")
    
    def drawSymbol(self, painter):
        margin = 9
        painter.setPen(QPen(QColor(255, 255, 255), 2.0, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        painter.drawLine(margin, margin, self.width() - margin, self.height() - margin)
        painter.drawLine(self.width() - margin, margin, margin, self.height() - margin)
    
    def play_button_sound(self):
        """Sobrescribir para que no reproduzca sonido"""
        pass

class CustomMaximizeButton(WindowControlButton):
    def __init__(self, parent=None):
        super().__init__("Maximizar", parent)
        self.current_mode = "normal"
        
    def getGradientColors(self):
        return ButtonColors.get_colors(self.current_mode, "maximize")
    
    def drawSymbol(self, painter):
        margin = 9
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        # + blanco
        painter.setPen(QPen(QColor(255, 255, 255), 2.0, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        painter.drawLine(center_x, margin, center_x, self.height() - margin)
        painter.drawLine(margin, center_y, self.width() - margin, center_y)
    
    def play_button_sound(self):
        """Sobrescribir para que no reproduzca sonido"""
        pass

class CustomMinimizeButton(WindowControlButton):
    def __init__(self, parent=None):
        super().__init__("Minimizar", parent)
        self.current_mode = "normal"
        
    def getGradientColors(self):
        return ButtonColors.get_colors(self.current_mode, "minimize")
    
    def drawSymbol(self, painter):
        margin = 9
        center_y = self.height() // 2
        painter.setPen(QPen(QColor(255, 255, 255), 2.0, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        painter.drawLine(margin, center_y, self.width() - margin, center_y)
    
    def play_button_sound(self):
        """Sobrescribir para que no reproduzca sonido"""
        pass

class CustomLoadButton(CustomBaseButton):
    def drawSymbol(self, painter):
        center_x = 13
        center_y = 13
        arrow_width = 10  # Aumentado
        arrow_height = 11  # Aumentado
        painter.setPen(QPen(QColor(156, 39, 176), 1.5, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        painter.setBrush(QColor(156, 39, 176))
        arrow_path = QPainterPath()
        arrow_path.moveTo(center_x, center_y + arrow_height//2)
        arrow_path.lineTo(center_x - arrow_width//2, center_y)
        arrow_path.lineTo(center_x - 2, center_y)
        arrow_path.lineTo(center_x - 2, center_y - arrow_height//2)
        arrow_path.lineTo(center_x + 2, center_y - arrow_height//2)
        arrow_path.lineTo(center_x + 2, center_y)
        arrow_path.lineTo(center_x + arrow_width//2, center_y)
        arrow_path.closeSubpath()
        painter.drawPath(arrow_path)

class CustomModeButton(CustomBaseButton):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_mode = "normal"
    def set_mode(self, mode):
        """Establece el modo actual del botón"""
        self.current_mode = mode
        self.update()  # Forzar el redibujado del botón
    def getGradientColors(self):
        if self.current_mode == "normal":
            return {
                0.0: QColor(255, 82, 82),  # Rojo claro
                0.4: QColor(244, 67, 54),  # Rojo medio
                0.6: QColor(229, 57, 53),  # Rojo medio-oscuro
                1.0: QColor(211, 47, 47)   # Rojo oscuro
            }
        elif self.current_mode == "comparison":
            return {
                0.0: QColor(66, 165, 245),  # Azul claro
                0.4: QColor(33, 150, 243),  # Azul medio
                0.6: QColor(30, 136, 229),  # Azul medio-oscuro
                1.0: QColor(25, 118, 210)   # Azul oscuro
            }
        else:  
            return {
                0.0: QColor(129, 199, 132),  # Verde claro
                0.4: QColor(76, 175, 80),    # Verde medio
                0.6: QColor(67, 160, 71),    # Verde medio-oscuro
                1.0: QColor(56, 142, 60)     # Verde oscuro
            }
    
    def drawSymbol(self, painter):
        center_x = 13
        center_y = 13
        if self.current_mode == "normal":
            # Símbolo de prohibición
            circle_radius = 8
            line_width = 2
            
            # Círculo exterior
            painter.setPen(QPen(QColor(255, 255, 255), line_width, Qt.PenStyle.SolidLine))
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawEllipse(QPoint(center_x, center_y), circle_radius, circle_radius)
            
            # Línea diagonal
            painter.setPen(QPen(QColor(255, 255, 255), line_width + 0.5, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
            painter.drawLine(
                center_x - circle_radius//2, center_y - circle_radius//2,
                center_x + circle_radius//2, center_y + circle_radius//2
            )
        elif self.current_mode == "comparison":
            # Dimensiones aumentadas de la carpeta
            folder_width = 16  # Aumentado de 12 a 16
            folder_height = 11  # Aumentado de 8 a 11
            tab_width = int(folder_width * 0.4)
            tab_height = 3  # Aumentado de 2 a 3
            corner_radius = 1.5  # Aumentado de 1 a 1.5
            
            # Punto base de la carpeta (centrado)
            x = center_x - folder_width//2
            y = center_y - folder_height//2
            
            # Sombra de la carpeta
            shadow_offset = 0.8  # Aumentado de 0.5 a 0.8
            shadow_path = QPainterPath()
            shadow_path.moveTo(x + corner_radius + shadow_offset, y + shadow_offset)
            shadow_path.lineTo(x + tab_width + shadow_offset, y + shadow_offset)
            shadow_path.quadTo(x + tab_width + 2 + shadow_offset, y + shadow_offset, 
                             x + tab_width + 2 + shadow_offset, y + 1 + shadow_offset)
            shadow_path.lineTo(x + folder_width - corner_radius + shadow_offset, y + 1 + shadow_offset)
            shadow_path.quadTo(x + folder_width + shadow_offset, y + 1 + shadow_offset,
                             x + folder_width + shadow_offset, y + 1 + corner_radius + shadow_offset)
            shadow_path.lineTo(x + folder_width + shadow_offset, y + folder_height - corner_radius + shadow_offset)
            shadow_path.quadTo(x + folder_width + shadow_offset, y + folder_height + shadow_offset,
                             x + folder_width - corner_radius + shadow_offset, y + folder_height + shadow_offset)
            shadow_path.lineTo(x + corner_radius + shadow_offset, y + folder_height + shadow_offset)
            painter.setPen(Qt.PenStyle.NoPen)
            painter.setBrush(QColor(0, 0, 0, 50))  # Sombra un poco más oscura
            painter.drawPath(shadow_path)
            folder_gradient = QLinearGradient(x, y, x, y + folder_height)
            folder_gradient.setColorAt(0.0, QColor(255, 215, 89))  # Amarillo más claro arriba
            folder_gradient.setColorAt(0.5, QColor(255, 205, 69))  # Amarillo medio
            folder_gradient.setColorAt(1.0, QColor(255, 195, 59))  # Amarillo más oscuro abajo
            folder_path = QPainterPath()
            folder_path.moveTo(x + corner_radius, y)
            folder_path.lineTo(x + tab_width, y)
            folder_path.quadTo(x + tab_width + 2, y, x + tab_width + 2, y + 1)
            folder_path.lineTo(x + folder_width - corner_radius, y + 1)
            folder_path.quadTo(x + folder_width, y + 1, x + folder_width, y + 1 + corner_radius)
            folder_path.lineTo(x + folder_width, y + folder_height - corner_radius)
            folder_path.quadTo(x + folder_width, y + folder_height, x + folder_width - corner_radius, y + folder_height)
            folder_path.lineTo(x + corner_radius, y + folder_height)
            folder_path.quadTo(x, y + folder_height, x, y + folder_height - corner_radius)
            folder_path.lineTo(x, y + corner_radius)
            folder_path.quadTo(x, y, x + corner_radius, y)
            painter.setBrush(folder_gradient)
            painter.setPen(QPen(QColor(255, 187, 36), 1.2))  # Borde un poco más grueso
            painter.drawPath(folder_path)
            highlight_gradient = QLinearGradient(x, y, x, y + folder_height * 0.4)  # Aumentado área de brillo
            highlight_gradient.setColorAt(0.0, QColor(255, 255, 255, 100))  # Brillo más intenso
            highlight_gradient.setColorAt(1.0, QColor(255, 255, 255, 0))
            highlight_path = QPainterPath()
            highlight_path.moveTo(x + corner_radius, y + 0.5)
            highlight_path.lineTo(x + folder_width - corner_radius, y + 0.5)
            highlight_path.quadTo(x + folder_width - 0.5, y + 0.5, x + folder_width - 0.5, y + corner_radius)
            highlight_path.lineTo(x + folder_width - 0.5, y + folder_height * 0.4)
            highlight_path.lineTo(x + 0.5, y + folder_height * 0.4)
            highlight_path.lineTo(x + 0.5, y + corner_radius)
            highlight_path.quadTo(x + 0.5, y + 0.5, x + corner_radius, y + 0.5)
            painter.setPen(Qt.PenStyle.NoPen)
            painter.setBrush(highlight_gradient)
            painter.drawPath(highlight_path)
            tab_gradient = QLinearGradient(x, y, x, y + tab_height)
            tab_gradient.setColorAt(0.0, QColor(255, 225, 99))  # Más claro
            tab_gradient.setColorAt(1.0, QColor(255, 215, 89))  # Más oscuro
            painter.setBrush(tab_gradient)
            painter.setPen(QPen(QColor(255, 187, 36), 1.2))
            tab_path = QPainterPath()
            tab_path.moveTo(x + tab_width, y)
            tab_path.lineTo(x + tab_width + 2, y)
            tab_path.lineTo(x + tab_width + 2, y + tab_height)
            tab_path.lineTo(x + tab_width, y + tab_height)
            painter.drawPath(tab_path)
        else: 
            symbol_width = 10
            symbol_height = 8
            line_width = 1.5
            painter.setPen(QPen(QColor(255, 255, 255), line_width, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
            points = [
                QPoint(center_x - symbol_width//2, center_y),  # Izquierda
                QPoint(center_x, center_y - symbol_height//2),  # Arriba
                QPoint(center_x + symbol_width//2, center_y),  # Derecha
                QPoint(center_x, center_y + symbol_height//2)   # Abajo
            ]
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawPolygon(points)

def crear_icono_svg(svg_data, size, add_gradient=True):
    from PyQt6.QtSvg import QSvgRenderer
    from PyQt6.QtGui import QIcon, QPixmap, QPainter, QLinearGradient, QColor, QRadialGradient
    from PyQt6.QtCore import QByteArray, Qt, QPointF, QRectF
    
    # Crear pixmap desde los datos SVG
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    # Convertir el SVG a QByteArray
    svg_bytes = QByteArray(svg_data.encode('utf-8'))
    
    # Renderizar SVG en el pixmap
    renderer = QSvgRenderer(svg_bytes)
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # Dibuja el icono base
    renderer.render(painter)
    
    if add_gradient:
        # Guardar el estado del pintor para restaurarlo después
        painter.save()
        
        # Efecto 1: Gradiente radial para simular iluminación 3D
        radial_gradient = QRadialGradient(
            QPointF(size * 0.3, size * 0.3),  # Centro del gradiente en 30%
            size * 0.7  # Radio del gradiente
        )
        radial_gradient.setColorAt(0, QColor(255, 255, 255, 60))  # Brillo en el centro
        radial_gradient.setColorAt(1, QColor(255, 255, 255, 0))   # Transparente en bordes
        
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceAtop)
        painter.fillRect(pixmap.rect(), radial_gradient)
        
        # Efecto 2: Sombra sutil en la parte inferior
        shadow_gradient = QLinearGradient(QPointF(0, size * 0.7), QPointF(0, size))
        shadow_gradient.setColorAt(0, QColor(0, 0, 0, 0))      # Transparente arriba
        shadow_gradient.setColorAt(1, QColor(0, 0, 0, 40))     # Sombra abajo
        
        painter.fillRect(QRectF(0, size * 0.7, size, size * 0.3), shadow_gradient)
        
        # Efecto 3: Brillo superior como en la versión original
        top_gradient = QLinearGradient(QPointF(0, 0), QPointF(0, size * 0.5))
        top_gradient.setColorAt(0, QColor(255, 255, 255, 80))  # Brillo arriba
        top_gradient.setColorAt(1, QColor(255, 255, 255, 0))   # Transparente abajo
        
        painter.fillRect(QRectF(0, 0, size, size * 0.5), top_gradient)
        
        # Restaurar el estado del pintor
        painter.restore()
    
    painter.end()
    
    if add_gradient:
        return QIcon(pixmap)
    else:
        return QIcon(pixmap), pixmap

def boton_PLAY(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 16 16"><path fill="#e1b241" d="M14.642 6.285c1.294.777 1.294 2.653 0 3.43l-9.113 5.468c-1.333.8-3.028-.16-3.029-1.715V2.532C2.5.978 4.196.018 5.53.818z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def boton_STOP(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><path fill="#e14841" d="M2 12c0-4.714 0-7.071 1.464-8.536C4.93 2 7.286 2 12 2s7.071 0 8.535 1.464C22 4.93 22 7.286 22 12s0 7.071-1.465 8.535C19.072 22 16.714 22 12 22s-7.071 0-8.536-1.465C2 19.072 2 16.714 2 12"/></svg>'''
    return crear_icono_svg(svg_data, size)

def boton_AJUSTES(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 28 28"><g fill="none"><path fill="url(#fluentColorSettings280)" d="M16.693 2.311A13 13 0 0 0 14.013 2q-1.384.016-2.704.311a.92.92 0 0 0-.716.8l-.209 1.877a1.707 1.707 0 0 1-2.371 1.376l-1.72-.757a.92.92 0 0 0-1.043.214a12.06 12.06 0 0 0-2.709 4.667a.92.92 0 0 0 .334 1.017l1.527 1.125a1.7 1.7 0 0 1 0 2.74l-1.527 1.128a.92.92 0 0 0-.334 1.016a12.1 12.1 0 0 0 2.707 4.672a.92.92 0 0 0 1.043.215l1.728-.759a1.7 1.7 0 0 1 1.526.086c.466.27.777.745.838 1.281l.208 1.877a.92.92 0 0 0 .702.796a11.7 11.7 0 0 0 5.413 0a.92.92 0 0 0 .702-.796l.208-1.88a1.693 1.693 0 0 1 2.366-1.37l1.727.759a.92.92 0 0 0 1.043-.215a12.1 12.1 0 0 0 2.707-4.667a.92.92 0 0 0-.334-1.017L23.6 15.37a1.7 1.7 0 0 1-.001-2.74l1.525-1.127a.92.92 0 0 0 .333-1.016a12.06 12.06 0 0 0-2.708-4.667a.92.92 0 0 0-1.043-.214l-1.72.757a1.7 1.7 0 0 1-.68.144a1.7 1.7 0 0 1-1.688-1.518l-.21-1.879a.92.92 0 0 0-.714-.799M14 18a4 4 0 1 1 0-8a4 4 0 0 1 0 8"/><defs><linearGradient id="fluentColorSettings280" x1="19.749" x2="6.326" y1="25.071" y2="4.173" gradientUnits="userSpaceOnUse"><stop stop-color="#70777d"/><stop offset="1" stop-color="#b9c0c7"/></linearGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def boton_BORRAR(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 48 48"><path fill="#e14841" fill-rule="evenodd" d="M16.336 5.188A8 8 0 0 1 23.37 1h1.26a8 8 0 0 1 7.034 4.188c3.28.067 5.861.169 7.744.264c2.67.134 5.364 1.58 6.188 4.41q.161.551.294 1.168c.54 2.524-1.377 4.585-3.751 4.785c-3.056.259-8.613.55-18.162.55c-9.547 0-15.104-.291-18.16-.55c-2.356-.199-4.338-2.273-3.658-4.821c.188-.707.42-1.35.663-1.92C3.83 6.713 6.21 5.598 8.522 5.47c1.813-.1 4.403-.21 7.814-.282M5.564 18.805a7 7 0 0 1-.695-.095c.821 12.048 1.592 18.537 2.07 21.774c.332 2.251 1.85 4.217 4.226 4.788c2.445.587 6.55 1.227 12.837 1.227c6.286 0 10.392-.64 12.837-1.227c2.375-.571 3.894-2.537 4.226-4.788c.478-3.237 1.249-9.73 2.07-21.782a7 7 0 0 1-.743.103c-3.165.267-8.811.56-18.415.56c-9.602 0-15.248-.293-18.413-.56M19.99 26.8a2 2 0 1 0-3.98.398l1 10a2 2 0 1 0 3.98-.398zm10.21-1.79a2 2 0 0 0-2.19 1.79l-1 10a2 2 0 1 0 3.98.399l1-10a2 2 0 0 0-1.79-2.19" clip-rule="evenodd"/></svg>'''
    return crear_icono_svg(svg_data, size)

def boton_SALVAR(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><path fill="#1d724a" fill-rule="evenodd" d="M12 15.75a.75.75 0 0 0 .75-.75V4.027l1.68 1.961a.75.75 0 1 0 1.14-.976l-3-3.5a.75.75 0 0 0-1.14 0l-3 3.5a.75.75 0 1 0 1.14.976l1.68-1.96V15c0 .414.336.75.75.75" clip-rule="evenodd"/><path fill="#1d724a" d="M16 9c-.702 0-1.053 0-1.306.169a1 1 0 0 0-.275.275c-.169.253-.169.604-.169 1.306V15a2.25 2.25 0 1 1-4.5 0v-4.25c0-.702 0-1.053-.169-1.306a1 1 0 0 0-.275-.275C9.053 9 8.702 9 8 9c-2.828 0-4.243 0-5.121.879C2 10.757 2 12.17 2 14.999v1c0 2.83 0 4.243.879 5.122C3.757 22 5.172 22 8 22h8c2.828 0 4.243 0 5.121-.879S22 18.828 22 16v-1c0-2.829 0-4.243-.879-5.121C20.243 9 18.828 9 16 9"/></svg>'''
    return crear_icono_svg(svg_data, size)

def boton_CARGAR(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><path fill="#1d5a72" fill-rule="evenodd" d="M12 1.25a.75.75 0 0 0-.75.75v10.973l-1.68-1.961a.75.75 0 1 0-1.14.976l3 3.5a.75.75 0 0 0 1.14 0l3-3.5a.75.75 0 1 0-1.14-.976l-1.68 1.96V2a.75.75 0 0 0-.75-.75" clip-rule="evenodd"/><path fill="#1d5a72" d="M14.25 9v.378a2.249 2.249 0 0 1 2.458 3.586l-3 3.5a2.25 2.25 0 0 1-3.416 0l-3-3.5A2.25 2.25 0 0 1 9.75 9.378V9H8c-2.828 0-4.243 0-5.121.879C2 10.757 2 12.172 2 15v1c0 2.828 0 4.243.879 5.121C3.757 22 5.172 22 8 22h8c2.828 0 4.243 0 5.121-.879C22 20.243 22 18.828 22 16v-1c0-2.828 0-4.243-.879-5.121C20.243 9 18.828 9 16 9z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_CARPETA(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><path fill="#f3c514" d="M1 4.5A2.5 2.5 0 0 1 3.5 2h2.086a1.5 1.5 0 0 1 1.06.44L8 3.792l-2.06 2.06A.5.5 0 0 1 5.585 6H1zM1 7v4.5A2.5 2.5 0 0 0 3.5 14h9a2.5 2.5 0 0 0 2.5-2.5v-5A2.5 2.5 0 0 0 12.5 4H9.207l-2.56 2.56A1.5 1.5 0 0 1 5.585 7z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_OTROS(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><g fill="none"><path fill="url(#fluentColorDocument320)" d="M17 2H8a3 3 0 0 0-3 3v22a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V12l-7-3z"/><path fill="url(#fluentColorDocument322)" fill-opacity="0.5" d="M17 2H8a3 3 0 0 0-3 3v22a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3V12l-7-3z"/><path fill="url(#fluentColorDocument321)" d="M17 10V2l10 10h-8a2 2 0 0 1-2-2"/><defs><linearGradient id="fluentColorDocument320" x1="20.4" x2="22.711" y1="2" y2="25.61" gradientUnits="userSpaceOnUse"><stop stop-color="#6ce0ff"/><stop offset="1" stop-color="#4894fe"/></linearGradient><linearGradient id="fluentColorDocument321" x1="21.983" x2="19.483" y1="6.167" y2="10.333" gradientUnits="userSpaceOnUse"><stop stop-color="#9ff0f9"/><stop offset="1" stop-color="#b3e0ff"/></linearGradient><radialGradient id="fluentColorDocument322" cx="0" cy="0" r="1" gradientTransform="rotate(133.108 13.335 7.491)scale(17.438 10.2853)" gradientUnits="userSpaceOnUse"><stop offset=".362" stop-color="#4a43cb"/><stop offset="1" stop-color="#4a43cb" stop-opacity="0"/></radialGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def boton_CONFIRMAR(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 48 48"><g fill="none"><path fill="url(#fluentColorCheckmarkCircle480)" d="M24 4c11.046 0 20 8.954 20 20s-8.954 20-20 20S4 35.046 4 24S12.954 4 24 4"/><path fill="url(#fluentColorCheckmarkCircle481)" d="M32.634 17.616a1.25 1.25 0 0 1 0 1.768l-11 11a1.25 1.25 0 0 1-1.768 0l-4.5-4.5a1.25 1.25 0 0 1 1.768-1.768l3.616 3.616l10.116-10.116a1.25 1.25 0 0 1 1.768 0"/><defs><linearGradient id="fluentColorCheckmarkCircle480" x1="5.429" x2="33.033" y1="11.5" y2="40.18" gradientUnits="userSpaceOnUse"><stop stop-color="#52d17c"/><stop offset="1" stop-color="#22918b"/></linearGradient><linearGradient id="fluentColorCheckmarkCircle481" x1="18.375" x2="21.586" y1="18.627" y2="33.741" gradientUnits="userSpaceOnUse"><stop stop-color="#fff"/><stop offset="1" stop-color="#e3ffd9"/></linearGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def boton_CERRAR(size=50):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><g fill="none"><path fill="url(#fluentColorDismissCircle240)" d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2"/><path fill="url(#fluentColorDismissCircle241)" d="m15.53 8.47l-.084-.073a.75.75 0 0 0-.882-.007l-.094.08L12 10.939l-2.47-2.47l-.084-.072a.75.75 0 0 0-.882-.007l-.094.08l-.073.084a.75.75 0 0 0-.007.882l.08.094L10.939 12l-2.47 2.47l-.072.084a.75.75 0 0 0-.007.882l.08.094l.084.073a.75.75 0 0 0 .882.007l.094-.08L12 13.061l2.47 2.47l.084.072a.75.75 0 0 0 .882.007l.094-.08l.073-.084a.75.75 0 0 0 .007-.882l-.08-.094L13.061 12l2.47-2.47l.072-.084a.75.75 0 0 0 .007-.882z"/><defs><linearGradient id="fluentColorDismissCircle240" x1="5.125" x2="18.25" y1="3.25" y2="22.625" gradientUnits="userSpaceOnUse"><stop stop-color="#ff7282"/><stop offset="1" stop-color="#ca2134"/></linearGradient><linearGradient id="fluentColorDismissCircle241" x1="8.685" x2="12.591" y1="12.332" y2="16.392" gradientUnits="userSpaceOnUse"><stop stop-color="#fdfdfd"/><stop offset="1" stop-color="#fecbe6"/></linearGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)

def Logo_ZConver(size=40):
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 24 24">
  <defs>
    <!-- Gradiente verde intenso -->
    <radialGradient id="bevel3d" cx="30%" cy="30%" r="70%">
      <stop offset="0%"  stop-color="#99e699" />
      <stop offset="30%" stop-color="#66cc66" />
      <stop offset="70%" stop-color="#339933" />
      <stop offset="100%" stop-color="#005c00" />
    </radialGradient>

    <!-- Sombra interna para la Z -->
    <filter id="innerGlowZ" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="0.8" result="blur"/>
      <feOffset dx="0" dy="0.5" result="offset"/>
      <feMerge>
        <feMergeNode in="offset"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Rombo principal -->
  <path fill="url(#bevel3d)"
        d="m13.666 1.429l6.75 3.98l.096.063l.093.078l.106.074a3.22 3.22 0 0 1 1.284 2.39l.005.204v7.284c0 1.175-.643 2.256-1.623 2.793l-6.804 4.302c-.98.538-2.166.538-3.2-.032l-6.695-4.237A3.23 3.23 0 0 1 2 15.502V8.217c0-1.106.57-2.128 1.476-2.705l6.95-4.098c1-.552 2.214-.552 3.24.015"/>

  <!-- Z flotante -->
  <path fill="white" filter="url(#innerGlowZ)"
        d="M14 7h-4a1 1 0 0 0-1 1l.007.117A1 1 0 0 0 10 9h2.382l-3.276 6.553A1 1 0 0 0 10 17h4a1 1 0 0 0 1-1l-.007-.117A1 1 0 0 0 14 15h-2.382l3.276-6.553A1 1 0 0 0 14 7"/>
</svg>'''
    return crear_icono_svg(svg_data, size)

def icono_CONVIRTIENDO(size=16):
    """
    Icono verde pequeño para archivos que se están convirtiendo
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><path fill="#1d721f" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75s9.75-4.365 9.75-9.75S17.385 2.25 12 2.25"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_COMPLETADO(size=16):
    """
    Icono verde con check para archivos completados
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><path fill="#1d721f" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75s9.75-4.365 9.75-9.75S17.385 2.25 12 2.25"/><path fill="white" d="m16.5 9.75l-5.25 5.25l-2.25-2.25l1.06-1.06l1.19 1.19l4.19-4.19z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_EN_COLA(size=16):
    """
    Icono amarillo para archivos en cola
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><path fill="#f3c514" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75s9.75-4.365 9.75-9.75S17.385 2.25 12 2.25"/><path fill="white" d="M12 6v6l4 2l-1 1.5l-5-3V6z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_ERROR(size=16):
    """
    Icono rojo para archivos con error
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><path fill="#d32f2f" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75s9.75-4.365 9.75-9.75S17.385 2.25 12 2.25"/><path fill="white" d="M12 8v4m0 4h.01"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_PENDIENTE(size=16):
    """
    Icono azul para archivos pendientes de conversión (recién arrastrados)
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><path fill="#2196f3" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75s9.75-4.365 9.75-9.75S17.385 2.25 12 2.25"/><path fill="white" d="M12 7v5l3 3l-1 1l-4-4V7z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_CANTIDAD_CONVERSION(size=16):
    """
    Icono azul para cantidad de conversiones simultáneas
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 24 24"><g fill="#2a98e7" fill-rule="evenodd" clip-rule="evenodd"><path d="M9.632 9.022c-.303.04-.398.106-.45.16c-.054.052-.12.147-.16.45c-.044.322-.045.76-.045 1.438v1.86c0 .678.001 1.116.045 1.438c.04.303.106.398.16.45c.**************.45.16c.322.044.76.045 1.438.045h1.86c.678 0 1.116-.001 1.438-.045c.303-.04.398-.106.45-.16c.054-.052.12-.147.16-.45c.044-.322.045-.76.045-1.438v-1.86c0-.678-.001-1.116-.045-1.438c-.04-.303-.106-.398-.16-.45c-.052-.054-.147-.12-.45-.16c-.322-.044-.76-.045-1.438-.045h-1.86c-.678 0-1.116.001-1.438.045m3.334 1.523a.698.698 0 0 0-1.135-.81l-1.329 1.86a.698.698 0 0 0 .568 1.103h.505l-.541.757a.698.698 0 0 0 1.135.811l1.329-1.86a.698.698 0 0 0-.568-1.104h-.505z"/><path d="M12.698 2.698a.698.698 0 0 0-1.396 0v2.79q-.764 0-1.395.017V2.698a.698.698 0 0 0-1.395 0v2.79q0 .056.008.108c-.936.115-1.585.353-2.078.846s-.731 1.142-.846 2.078a1 1 0 0 0-.108-.008h-2.79a.698.698 0 0 0 0 1.395h2.807q-.016.63-.016 1.395H2.698a.698.698 0 0 0 0 1.396h2.79q0 .764.017 1.395H2.698a.698.698 0 0 0 0 1.395h2.79a1 1 0 0 0 .108-.008c.115.936.353 1.585.846 2.078s1.142.731 2.078.846a1 1 0 0 0-.008.108v2.79a.698.698 0 0 0 1.395 0v-2.807q.63.016 1.395.016v2.791a.698.698 0 0 0 1.396 0v-2.79q.764 0 1.395-.017v2.807a.698.698 0 0 0 1.395 0v-2.79a1 1 0 0 0-.008-.108c.936-.115 1.585-.353 2.078-.846s.731-1.142.846-2.078q.053.009.108.008h2.79a.698.698 0 0 0 0-1.395h-2.807q.016-.63.016-1.395h2.791a.698.698 0 0 0 0-1.396h-2.79q0-.764-.017-1.395h2.807a.698.698 0 0 0 0-1.395h-2.79a1 1 0 0 0-.108.008c-.115-.936-.353-1.585-.846-2.078s-1.142-.731-2.078-.846a1 1 0 0 0 .008-.108v-2.79a.698.698 0 0 0-1.395 0v2.807a56 56 0 0 0-1.395-.016zm-3.252 4.94c.426-.057.96-.057 1.578-.057h1.952c.619 0 1.151 0 1.578.058c.458.061.896.2 1.252.555c.355.356.494.794.555 1.252c.058.426.058.96.058 1.578v1.952c0 .619 0 1.151-.058 1.578c-.061.458-.2.896-.555 1.252c-.356.355-.794.494-1.252.555c-.427.058-.96.058-1.578.058h-1.952c-.619 0-1.152 0-1.578-.058c-.458-.061-.896-.2-1.252-.555c-.355-.356-.494-.794-.555-1.252c-.058-.427-.058-.96-.058-1.578v-1.952c0-.619 0-1.152.058-1.578c.061-.458.2-.896.555-1.252c.356-.355.794-.494 1.252-.555"/></g></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_RUTA_CONVERSION(size=16):
    """
    Icono morado para ruta de conversión
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#7b1ce3" d="M17 11h3a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2h-3a2 2 0 0 0-2 2v1H9.01V5a2 2 0 0 0-2-2H4c-1.1 0-2 .9-2 2v4a2 2 0 0 0 2 2h3a2 2 0 0 0 2-2V8h2v7.01c0 1.65 1.34 2.99 2.99 2.99H15v1a2 2 0 0 0 2 2h3a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2h-3a2 2 0 0 0-2 2v1h-1.01c-.54 0-.99-.45-.99-.99V8h2v1c0 1.1.9 2 2 2"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_CONFIGURACION(size=16):
    """
    Icono verde para configuraciones
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="#4caf50" d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97s-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.66-.07 1s.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64z"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_DISCO(size=16):
    """
    Icono naranja para disco/almacenamiento
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 14 14"><path fill="#7c3aed" fill-rule="evenodd" d="M4.865.711a.25.25 0 0 0-.272-.248l-.3.03a1.5 1.5 0 0 0-.9.427L.92 3.377c-.245.244-.4.565-.434.912A28 28 0 0 0 .334 7c0 1.425.156 2.817.304 4.13q.045.39.085.772A1.53 1.53 0 0 0 2.08 13.26l.55.062a.25.25 0 0 0 .278-.248V9.55c0-.965.782-1.747 1.747-1.747l4.693.002c.965 0 1.746.782 1.746 1.746v3.522c0 .15.13.266.279.248l.55-.062a1.53 1.53 0 0 0 1.355-1.357l.085-.773c.148-1.312.304-2.704.304-4.129s-.156-2.817-.304-4.129l-.085-.773A1.53 1.53 0 0 0 11.92.74l-.39-.044a.25.25 0 0 0-.279.249v2.406c0 .897-.728 1.625-1.625 1.625l-3.138-.002A1.625 1.625 0 0 1 4.865 3.35zm5.137.037A.25.25 0 0 0 9.78.5A27 27 0 0 0 7 .334q-.324 0-.646.01a.25.25 0 0 0-.24.25V3.35c0 .208.169.375.376.375l3.137.002a.375.375 0 0 0 .375-.375zM7 13.666c-.889 0-1.766-.064-2.619-.15a.25.25 0 0 1-.224-.247v-3.72c0-.273.223-.496.497-.496l4.693.002c.274 0 .496.222.496.496v3.718a.25.25 0 0 1-.224.248c-.852.085-1.73.15-2.619.15" clip-rule="evenodd"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_RELOJ(size=16):
    """
    Icono de reloj para mostrar antes de la hora
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 14 14"><path fill="#ff5252" fill-rule="evenodd" d="M3.603 1.412A.75.75 0 1 0 2.897.088A9.4 9.4 0 0 0 .182 2.261a.75.75 0 1 0 1.136.978a7.9 7.9 0 0 1 2.285-1.827M7 14A6 6 0 1 0 7 2a6 6 0 0 0 0 12M10.088.398a.75.75 0 0 1 1.015-.31a9.4 9.4 0 0 1 2.715 2.173a.75.75 0 0 1-1.136.978a7.9 7.9 0 0 0-2.285-1.827a.75.75 0 0 1-.309-1.014M7.625 5a.625.625 0 1 0-1.25 0v3c0 .345.28.625.625.625h2.5a.625.625 0 1 0 0-1.25H7.625z" clip-rule="evenodd"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_CPU(size=16):
    """
    Icono de CPU para mostrar antes de la temperatura
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 512 512"><path fill="#f29a36" d="M416 0c-52.9 0-96 43.1-96 96s43.1 96 96 96s96-43.1 96-96s-43.1-96-96-96m0 128c-17.7 0-32-14.3-32-32s14.3-32 32-32s32 14.3 32 32s-14.3 32-32 32m-160-16C256 50.1 205.9 0 144 0S32 50.1 32 112v166.5C12.3 303.2 0 334 0 368c0 79.5 64.5 144 144 144s144-64.5 144-144c0-34-12.3-64.9-32-89.5zM144 448c-44.1 0-80-35.9-80-80c0-25.5 12.2-48.9 32-63.8V112c0-26.5 21.5-48 48-48s48 21.5 48 48v192.2c19.8 14.8 32 38.3 32 63.8c0 44.1-35.9 80-80 80m16-125.1V112c0-8.8-7.2-16-16-16s-16 7.2-16 16v210.9c-18.6 6.6-32 24.2-32 45.1c0 26.5 21.5 48 48 48s48-21.5 48-48c0-20.9-13.4-38.5-32-45.1"/></svg>'''
    return crear_icono_svg(svg_data, size)

def icono_CANCELAR(size=16):
    """
    Icono rojo de cancelar para detener conversión de archivo específico
    """
    svg_data = '''<svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 20 20"><g fill="none"><path fill="url(#fluentColorDismissCircle200)" d="M10 2a8 8 0 1 1 0 16a8 8 0 0 1 0-16"/><path fill="url(#fluentColorDismissCircle201)" fill-rule="evenodd" d="M7.146 7.146a.5.5 0 0 1 .708 0L10 9.293l2.146-2.147a.5.5 0 0 1 .708.708L10.707 10l2.147 2.146a.5.5 0 0 1-.708.708L10 10.707l-2.146 2.147a.5.5 0 0 1-.708-.708L9.293 10L7.146 7.854a.5.5 0 0 1 0-.708" clip-rule="evenodd"/><defs><linearGradient id="fluentColorDismissCircle200" x1="4.5" x2="15" y1="3" y2="18.5" gradientUnits="userSpaceOnUse"><stop stop-color="#ff7282"/><stop offset="1" stop-color="#ca2134"/></linearGradient><linearGradient id="fluentColorDismissCircle201" x1="7.348" x2="10.473" y1="10.265" y2="13.514" gradientUnits="userSpaceOnUse"><stop stop-color="#fdfdfd"/><stop offset="1" stop-color="#fecbe6"/></linearGradient></defs></g></svg>'''
    return crear_icono_svg(svg_data, size)