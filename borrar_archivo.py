from PyQt6.QtCore import QTimer, Qt

def on_borrarButton_clicked(listWidget, currently_converting):
    indices = sorted([index.row() for index in listWidget.selectedIndexes()], reverse=True)
    archivos_en_conversión = False
    archivos_borrados = False
    
    for row in indices:
        item = listWidget.item(row)
        file_path = item.data(Qt.ItemDataRole.UserRole)
        
        # No permitir borrar archivos en conversión activa
        if "Convirtiendo" in item.text() or file_path in currently_converting:
            archivos_en_conversión = True
            continue
            
        # Verificar si está completado (tiene icono)
        widget = listWidget.itemWidget(item)
        is_completed = False
        if widget and hasattr(widget, 'icon_label'):
            is_completed = not widget.icon_label.pixmap().isNull() if widget.icon_label.pixmap() else False
        
        # Si está completado, solo ocultar
        if is_completed:
            item.setHidden(True)
            archivos_borrados = True
        # Si no está completado ni en conversión, borrar normalmente
        else:
            listWidget.takeItem(row)
            archivos_borrados = True

    # Si se borraron archivos, limpiar la cola de trabajos
    if archivos_borrados:
        # Obtener la ventana principal para acceder al método clean_job_queue
        main_window = listWidget.window()
        if hasattr(main_window, 'clean_job_queue'):
            main_window.clean_job_queue()

    if archivos_en_conversión:
        listWidget.setToolTip("No se puede borrar archivos mientras están en conversión.")
        QTimer.singleShot(3000, lambda: listWidget.setToolTip(""))

def borrar_archivo_seleccionado(listWidget, currently_converting, actualizar_contador):
    items_seleccionados = listWidget.selectedItems()
    archivos_en_conversión = False
    archivos_borrados = False
    
    for item in items_seleccionados:
        file_path = item.data(Qt.ItemDataRole.UserRole)
        
        # No permitir borrar archivos en conversión activa
        if "Convirtiendo" in item.text() or file_path in currently_converting:
            archivos_en_conversión = True
            continue
            
        # Si está completado, solo ocultar
        if "Completado" in item.text():
            item.setHidden(True)
            archivos_borrados = True
        # Si no está completado ni en conversión, borrar normalmente
        else:
            row = listWidget.row(item)
            listWidget.takeItem(row)
            archivos_borrados = True

    # Si se borraron archivos, limpiar la cola de trabajos
    if archivos_borrados:
        # Obtener la ventana principal para acceder al método clean_job_queue
        main_window = listWidget.window()
        if hasattr(main_window, 'clean_job_queue'):
            main_window.clean_job_queue()

    if archivos_en_conversión:
        listWidget.setToolTip("No se puede borrar archivos mientras están en conversión.")
        QTimer.singleShot(3000, lambda: listWidget.setToolTip(""))
    
    actualizar_contador()