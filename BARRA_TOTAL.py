from PyQt6.QtWidgets import QProgressBar
from PyQt6.QtGui import QPainter, QColor, QPainterPath, QPen, QLinearGradient, QFont
from PyQt6.QtCore import Qt, QRectF, pyqtProperty

class BarraTotal(QProgressBar):
    """
    Barra de progreso personalizada completa con bordes redondeados, efecto 3D
    y funcionalidad adicional para progreso de archivos
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setTextVisible(False)
        self.setStyleSheet("""
            QProgressBar {
                background-color: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)
        # Propiedades principales
        self.base_color = QColor("#0078d7")  # Color base azul Microsoft
        self.border_radius = 5             # Radio de borde (reducido de 10 a 5)
        self.use_3d_effect = True           # Habilitar efecto 3D por defecto
        self.show_percentage = True         # Controlar la visibilidad del porcentaje
        self.font_size = 10                 # Tamaño de fuente del porcentaje
        
        # Funcionalidad adicional para progreso de archivos
        self.file_progress = 0              # Progreso de archivos
        self.show_file_progress = False     # Control de visibilidad del progreso de archivos
        self.file_progress_color = QColor(0, 255, 0)  # Color verde para archivos

        # Color de fondo para BarraTotal (añadido para mostrar el recorrido total)
        self.background_fill_color = QColor(50, 50, 50, 100) # Gris oscuro semitransparente

    def setBaseColor(self, color):
        """Establece el color base de la barra de progreso"""
        try:
            if isinstance(color, str):
                self.base_color = QColor(color)
            elif isinstance(color, QColor):
                self.base_color = color
            else:
                self.base_color = QColor("#0078d7")  # Color fallback
            self.update()
        except Exception as e:
            print(f"Error al establecer color: {e}")
            self.base_color = QColor("#0078d7")  # Color fallback

    def set_color(self, color):
        """Método de compatibilidad con código anterior"""
        print(f"BarraTotal: Estableciendo color a {color}")
        self.setBaseColor(color)

    def setBorderRadius(self, radius):
        """Establece el radio de borde redondeado"""
        self.border_radius = radius
        self.update()
    
    def set3DEffect(self, enabled):
        """Habilita o deshabilita el efecto 3D"""
        self.use_3d_effect = enabled
        self.update()
    
    def setShowPercentage(self, show):
        """Controla si se muestra o no el porcentaje"""
        self.show_percentage = show
        self.update()
    
    def setFontSize(self, size):
        """Establece el tamaño de fuente del porcentaje"""
        self.font_size = size
        self.update()
    
    def setFileProgress(self, value):
        """Establece el progreso de archivos"""
        self.file_progress = value
        self.update()
    
    def setShowFileProgress(self, show):
        """Controla si se muestra el progreso de archivos"""
        self.show_file_progress = show
        self.update()
    
    def setFileProgressColor(self, color):
        """Establece el color del progreso de archivos"""
        if isinstance(color, str):
            self.file_progress_color = QColor(color)
        elif isinstance(color, QColor):
            self.file_progress_color = color
        self.update()
    
    def setMiniMode(self, enabled):
        """Activa el modo mini con configuraciones optimizadas"""
        if enabled:
            self.setBorderRadius(7)
            self.setFontSize(8)
            self.setShowFileProgress(True)
        else:
            self.setBorderRadius(10)
            self.setFontSize(10)
            self.setShowFileProgress(False)
    
    # Propiedad para animación si se requiere
    @pyqtProperty(QColor)
    def color(self):
        return self.base_color
        
    @color.setter
    def color(self, color):
        self.setBaseColor(color)

    def paintEvent(self, event):
        """Dibuja la barra de progreso personalizada con efecto 3D"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        width = self.width()
        height = self.height()
        
        # Primero dibujar el fondo de la barra para mostrar el recorrido total
        background_path = QPainterPath()
        background_path.addRoundedRect(QRectF(0, 0, width, height), self.border_radius, self.border_radius)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(self.background_fill_color)
        painter.drawPath(background_path)

        # Color base con opacidad 80%
        base_color = QColor(self.base_color) # Asegúrate de que esta línea esté presente
        base_color.setAlphaF(0.8)
        
        # Calcular el ancho de la barra según el progreso
        progress = self.value() / (self.maximum() - self.minimum()) if (self.maximum() - self.minimum()) > 0 else 0
        progress_width = int(width * progress)
        
        # Si hay progreso, dibujar el rectángulo de progreso
        if progress_width > 0:
            # Crear y dibujar el rectángulo de progreso (cuadrado, no redondeado)
            progress_path = QPainterPath()
            progress_path.addRect(0, 0, progress_width, height)
            
            # Intersectar el rectángulo de progreso con el contenedor redondeado
            progress_path = progress_path.intersected(background_path) # Asegúrate de que se interseca con background_path
            
            if self.use_3d_effect:
                # Crear gradiente para efecto 3D
                gradient = QLinearGradient(0, 0, 0, height)
                
                # Versión más brillante del color base para la parte superior
                bright_color = QColor(self.base_color)
                h, s, v, a = bright_color.getHsv()
                bright_color.setHsv(h, s, min(v + 30, 255), a)
                
                # Versión más oscura del color base para la parte inferior
                dark_color = QColor(self.base_color)
                h, s, v, a = dark_color.getHsv()
                dark_color.setHsv(h, s, max(v - 20, 0), a)
                
                gradient.setColorAt(0, bright_color)
                gradient.setColorAt(1, dark_color)
                
                # Dibujar el progreso con gradiente
                painter.fillPath(progress_path, gradient)
                
                # Efecto de brillo removido para apariencia mate
                # shine_gradient = QLinearGradient(0, 0, 0, height * 0.5)
                # shine_gradient.setColorAt(0, QColor(255, 255, 255, 160))
                # shine_gradient.setColorAt(0.3, QColor(255, 255, 255, 80))
                # shine_gradient.setColorAt(0.5, QColor(255, 255, 255, 0))
                # highlight_path = QPainterPath()
                # highlight_path.addRect(QRectF(0, 0, progress_width, height * 0.5))
                # highlight_path = highlight_path.intersected(progress_path)
                # painter.fillPath(highlight_path, shine_gradient)
                
                # Añadir sombra sutil en la parte inferior (cuadrada, siguiendo el progreso)
                shadow_gradient = QLinearGradient(0, height * 0.7, 0, height)
                shadow_gradient.setColorAt(0, QColor(0, 0, 0, 0))
                shadow_gradient.setColorAt(1, QColor(0, 0, 0, 40))
                
                shadow_path = QPainterPath()
                shadow_path.addRect(QRectF(0, height * 0.7, progress_width, height * 0.3))
                
                # Intersectar con el progreso para mantener los bordes correctos
                shadow_path = shadow_path.intersected(progress_path)
                
                painter.fillPath(shadow_path, shadow_gradient)
            else:
                # Dibujar el progreso con color plano (modo original)
                painter.fillPath(progress_path, base_color)
        
        # Dibujar el progreso de archivos si está habilitado
        if self.show_file_progress and self.file_progress > 0:
            painter.setPen(Qt.PenStyle.NoPen)
            painter.setBrush(self.file_progress_color)

            # Crear paths para el progreso de archivos
            file_progress_path = QPainterPath()
            file_progress_path.addRoundedRect(QRectF(1, 1, width - 2, height - 2), 
                                            self.border_radius - 1, self.border_radius - 1)
            file_progress_path.addRoundedRect(QRectF(3, 3, width - 6, height - 6), 
                                            self.border_radius - 3, self.border_radius - 3)
            painter.drawPath(file_progress_path)

            # Dibujar arco circular para el progreso de archivos
            angle = int(360 * self.file_progress / 100)
            painter.setPen(QPen(self.file_progress_color, 2))
            painter.drawArc(QRectF(1, 1, width - 2, height - 2), 90 * 16, -angle * 16)

        # Mostrar el porcentaje si está habilitado
        if self.show_percentage:
            text = f"{self.value()}%"
            painter.setPen(QColor(255, 255, 255))  # Color blanco para el texto
            font = QFont()
            font.setBold(True)
            font.setPointSize(self.font_size)
            painter.setFont(font)

            font_metrics = painter.fontMetrics()
            text_width = font_metrics.horizontalAdvance(text)
            text_height = font_metrics.height()

            text_x = (width - text_width) / 2
            text_y = (height + text_height) / 2 - font_metrics.descent()

            painter.drawText(int(text_x), int(text_y), text)

        painter.end()

    def resizeEvent(self, event):
        """Mantener la posición relativa de las barras durante el redimensionamiento"""
        super().resizeEvent(event)


# Alias para compatibilidad con código anterior
class RoundedProgressBar(BarraTotal):
    """
    Alias para compatibilidad con código anterior
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Activar modo mini por defecto para mantener compatibilidad
        self.setMiniMode(True)

class BarraMiniExplorador(BarraTotal):
    """
    Barra de progreso mini especializada para el explorador con fondo visible
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        # Configuraciones específicas para la barra mini del explorador
        self.setFixedSize(80, 15)  # Tamaño fijo para la barra mini
        self.setBorderRadius(7)    # Radio de borde más pequeño
        self.setFontSize(8)        # Fuente más pequeña
        self.setShowPercentage(True)
        self.background_color = QColor(40, 40, 40, 180)  # Fondo oscuro semitransparente
        
    def paintEvent(self, event):
        """
        Dibuja la barra con un fondo visible pero manteniendo el estilo de progreso de BarraTotal
        """
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        width = self.width()
        height = self.height()
        
        # Dibujar el fondo con bordes redondeados
        background_path = QPainterPath()
        background_path.addRoundedRect(QRectF(0, 0, width, height), self.border_radius, self.border_radius)
        
        # Rellenar con color de fondo
        painter.setPen(Qt.PenStyle.NoPen)
        painter.setBrush(self.background_color)
        painter.drawPath(background_path)
        
        # Calcular el ancho de la barra según el progreso
        progress = self.value() / (self.maximum() - self.minimum()) if (self.maximum() - self.minimum()) > 0 else 0
        progress_width = int(width * progress)
        
        # Si hay progreso, dibujar el rectángulo de progreso
        if progress_width > 0:
            # Crear y dibujar el rectángulo de progreso (cuadrado, no redondeado)
            progress_path = QPainterPath()
            progress_path.addRect(0, 0, progress_width, height)
            
            # Intersectar el rectángulo de progreso con el contenedor redondeado
            progress_path = progress_path.intersected(background_path)
            
            if self.use_3d_effect:
                # Crear gradiente para efecto 3D
                gradient = QLinearGradient(0, 0, 0, height)
                
                # Versión más brillante del color base para la parte superior
                bright_color = QColor(self.base_color)
                h, s, v, a = bright_color.getHsv()
                bright_color.setHsv(h, s, min(v + 30, 255), a)
                
                # Versión más oscura del color base para la parte inferior
                dark_color = QColor(self.base_color)
                h, s, v, a = dark_color.getHsv()
                dark_color.setHsv(h, s, max(v - 20, 0), a)
                
                gradient.setColorAt(0, bright_color)
                gradient.setColorAt(1, dark_color)
                
                # Dibujar el progreso con gradiente
                painter.fillPath(progress_path, gradient)
                
                # Efecto de brillo removido para apariencia mate
                # shine_gradient = QLinearGradient(0, 0, 0, height * 0.5)
                # shine_gradient.setColorAt(0, QColor(255, 255, 255, 160))
                # shine_gradient.setColorAt(0.3, QColor(255, 255, 255, 80))
                # shine_gradient.setColorAt(0.5, QColor(255, 255, 255, 0))
                # highlight_path = QPainterPath()
                # highlight_path.addRect(QRectF(0, 0, progress_width, height * 0.5))
                # highlight_path = highlight_path.intersected(progress_path)
                # painter.fillPath(highlight_path, shine_gradient)
            else:
                # Dibujar el progreso con color plano
                base_color = QColor(self.base_color)
                base_color.setAlphaF(0.8)
                painter.fillPath(progress_path, base_color)
        
        # Mostrar el porcentaje si está habilitado
        if self.show_percentage:
            text = f"{self.value()}%"
            painter.setPen(QColor(255, 255, 255))  # Color blanco para el texto
            font = QFont()
            font.setBold(True)
            font.setPointSize(self.font_size)
            painter.setFont(font)

            font_metrics = painter.fontMetrics()
            text_width = font_metrics.horizontalAdvance(text)
            text_height = font_metrics.height()

            text_x = (width - text_width) / 2
            text_y = (height + text_height) / 2 - font_metrics.descent()

            painter.drawText(int(text_x), int(text_y), text)

        painter.end()