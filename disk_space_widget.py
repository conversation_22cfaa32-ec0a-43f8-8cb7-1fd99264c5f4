import os
import shutil
from PyQt6.QtWidgets import QWidget, QHBoxLayout, QLabel, QGraphicsDropShadowEffect
from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtGui import QFont, QColor

class DockDiskWidget(QWidget):
    """Widget de espacio en disco para el DockSuperior con estilo igual al de temperatura"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_path = None
        self.setup_ui()
        self.setup_timer()
    def setup_ui(self):
        """Configura la interfaz del widget con el mismo estilo que el widget de temperatura"""
        self.setFixedHeight(28)  # Mismo tamaño que el widget de temperatura
        
        # Estilo igual al widget de temperatura - rectángulo gris redondeado
        self.setStyleSheet("""
            background-color: rgba(40, 40, 40, 220);
            border-radius: 12px;
            border: none;
        """)
        
        # Efecto de sombra igual al widget de temperatura
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(8)
        shadow.setXOffset(0)
        shadow.setYOffset(2)
        shadow.setColor(QColor(0, 0, 0, 100))
        self.setGraphicsEffect(shadow)
        
        # Layout horizontal igual al widget de temperatura
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 0, 8, 0)
        layout.setSpacing(4)
        
        # Icono del disco
        self.disk_icon = QLabel()
        from CREAR import icono_DISCO
        disk_icon_pixmap = icono_DISCO(16).pixmap(16, 16)
        self.disk_icon.setPixmap(disk_icon_pixmap)
        self.disk_icon.setStyleSheet("background: transparent; border: none;")
        
        # Label para el espacio disponible - mismo estilo que temperatura
        self.space_label = QLabel("--")
        self.space_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.space_label.setStyleSheet("""
            color: rgba(255, 255, 255, 200); 
            font-size: 11px; 
            font-weight: normal;
            background: transparent;
            border: none;
        """)
        
        layout.addWidget(self.disk_icon)
        layout.addWidget(self.space_label)
    
    def setup_timer(self):
        """Configura el timer para actualización en tiempo real con intervalos optimizados"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_disk_space_async)
        # Actualizar cada 10 segundos para reducir carga en el hilo principal
        self.update_timer.start(10000)
    
    def set_path(self, path):
        """Establece la ruta del disco a monitorear"""
        if path and os.path.exists(path):
            self.current_path = path
            self.update_disk_space()
        else:
            self.current_path = None
            self.space_label.setText("--")
    
    def update_disk_space_async(self):
        """Actualiza el espacio disponible en disco de forma asíncrona"""
        if not self.current_path:
            return
        
        # Ejecutar en un hilo separado para no bloquear la UI
        from PyQt6.QtCore import QThread, QObject, pyqtSignal
        
        class DiskWorker(QObject):
            finished = pyqtSignal(str, str)  # text, color
            
            def __init__(self, path):
                super().__init__()
                self.path = path
            
            def run(self):
                try:
                    total, used, free = shutil.disk_usage(self.path)
                    
                    free_gb = free / (1024**3)
                    
                    if free_gb >= 1:
                        space_text = f"{free_gb:.1f} GB"
                    else:
                        free_mb = free / (1024**2)
                        space_text = f"{free_mb:.0f} MB"
                    
                    # Cambiar color según el espacio disponible
                    if free_gb < 1:
                        color = "#ff5722"  # Rojo
                    elif free_gb < 5:
                        color = "#ff9800"  # Naranja
                    else:
                        color = "rgba(255, 255, 255, 200)"  # Blanco
                    
                    self.finished.emit(space_text, color)
                    
                except Exception:
                    self.finished.emit("Error", "#ff5722")
        
        # Crear worker y hilo
        if not hasattr(self, 'disk_thread') or not self.disk_thread.isRunning():
            self.disk_thread = QThread()
            self.disk_worker = DiskWorker(self.current_path)
            self.disk_worker.moveToThread(self.disk_thread)
            self.disk_thread.started.connect(self.disk_worker.run)
            self.disk_worker.finished.connect(self.update_disk_display)
            self.disk_worker.finished.connect(self.disk_thread.quit)
            self.disk_thread.start()
    
    def update_disk_display(self, text, color):
        """Actualiza la pantalla con los datos del disco"""
        self.space_label.setText(text)
        self.space_label.setStyleSheet(f"""
            color: {color}; 
            font-size: 11px; 
            font-weight: normal;
            background: transparent;
            border: none;
        """)
    
    def update_disk_space(self):
        """Método mantenido por compatibilidad - redirige al asíncrono"""
        self.update_disk_space_async()
    
    def update_text_color(self, is_light_mode):
        """Actualiza el color del texto según el modo de interfaz"""
        # Mantener el estilo consistente con temperatura y hora
        # El color se mantiene dinámico basado en el espacio disponible
        # pero respetando el modo de interfaz
        if hasattr(self, 'space_label'):
            current_text = self.space_label.text()
            if current_text != "--" and current_text != "Error":
                # Recalcular el color basado en el espacio actual
                self.update_disk_space()
    
    def get_disk_info(self):
        """Obtiene información detallada del disco (para tooltip si se necesita)"""
        if not self.current_path:
            return None
        
        try:
            total, used, free = shutil.disk_usage(self.current_path)
            return {
                'total': total,
                'used': used,
                'free': free,
                'path': self.current_path
            }
        except:
            return None


class DiskSpaceWidget(QWidget):
    """Widget que muestra el espacio disponible en disco en tiempo real"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_path = None
        self.setup_ui()
        self.setup_timer()
    
    def setup_ui(self):
        """Configura la interfaz del widget"""
        # Usar el mismo tamaño que los ComboBox
        self.setFixedHeight(22)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # Layout horizontal
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 8, 2)  # Mismo padding que los ComboBox
        layout.setSpacing(4)
        
        # Icono del disco
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(16, 16)  # Mismo tamaño que iconos de ComboBox
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Establecer el icono del disco
        from CREAR import icono_DISCO
        icon = icono_DISCO(16)
        self.icon_label.setPixmap(icon.pixmap(16, 16))
        
        # Label para el texto del espacio
        self.space_label = QLabel("--")
        self.space_label.setAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft)
        
        # Configurar fuente igual que los ComboBox
        font = QFont()
        font.setBold(True)
        font.setPointSize(10)  # Mismo tamaño que ComboBox
        self.space_label.setFont(font)
        
        # Estilo igual que los ComboBox
        self.setStyleSheet("""
            QWidget {
                border: none;
                border-radius: 12px;
                background-color: rgba(40, 40, 40, 220);
                min-width: 80px;
                max-width: 120px;
            }
            QWidget:hover {
                background-color: rgba(60, 60, 60, 220);
            }
            QLabel {
                color: white;
                background: transparent;
            }
        """)
        
        # Agregar widgets al layout
        layout.addWidget(self.icon_label)
        layout.addWidget(self.space_label)
        
        # Efecto de sombra igual que los ComboBox
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(8)
        shadow.setXOffset(0)
        shadow.setYOffset(2)
        shadow.setColor(QColor(0, 0, 0, 100))
        self.setGraphicsEffect(shadow)
    
    def setup_timer(self):
        """Configura el timer para actualización en tiempo real con intervalos optimizados"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_disk_space_async)
        # Actualizar cada 10 segundos para reducir carga en el hilo principal
        self.update_timer.start(10000)
    
    def set_path(self, path):
        """Establece la ruta del disco a monitorear"""
        if path and os.path.exists(path):
            self.current_path = path
            self.update_disk_space()
        else:
            self.current_path = None
            self.space_label.setText("--")
    
    def update_disk_space_async(self):
        """Actualiza el espacio disponible en disco de forma asíncrona"""
        if not self.current_path:
            return
        
        # Ejecutar en un hilo separado para no bloquear la UI
        from PyQt6.QtCore import QThread, QObject, pyqtSignal
        
        class DiskWorker(QObject):
            finished = pyqtSignal(str, str)  # text, color
            
            def __init__(self, path):
                super().__init__()
                self.path = path
            
            def run(self):
                try:
                    total, used, free = shutil.disk_usage(self.path)
                    
                    free_gb = free / (1024**3)
                    
                    if free_gb >= 1:
                        space_text = f"{free_gb:.1f} GB"
                    else:
                        free_mb = free / (1024**2)
                        space_text = f"{free_mb:.0f} MB"
                    
                    # Cambiar color según el espacio disponible
                    if free_gb < 1:
                        color = "#ff5722"  # Rojo
                    elif free_gb < 5:
                        color = "#ff9800"  # Naranja
                    else:
                        color = "rgba(255, 255, 255, 200)"  # Blanco
                    
                    self.finished.emit(space_text, color)
                    
                except Exception:
                    self.finished.emit("Error", "#ff5722")
        
        # Crear worker y hilo
        if not hasattr(self, 'disk_thread_2') or not self.disk_thread_2.isRunning():
            self.disk_thread_2 = QThread()
            self.disk_worker_2 = DiskWorker(self.current_path)
            self.disk_worker_2.moveToThread(self.disk_thread_2)
            self.disk_thread_2.started.connect(self.disk_worker_2.run)
            self.disk_worker_2.finished.connect(self.update_disk_display_2)
            self.disk_worker_2.finished.connect(self.disk_thread_2.quit)
            self.disk_thread_2.start()
    
    def update_disk_display_2(self, text, color):
        """Actualiza la pantalla con los datos del disco"""
        self.space_label.setText(text)
        self.space_label.setStyleSheet(f"color: {color}; background: transparent;")
    
    def update_disk_space(self):
        """Método mantenido por compatibilidad - redirige al asíncrono"""
        self.update_disk_space_async()
    
    def get_disk_info(self):
        """Obtiene información detallada del disco (para tooltip si se necesita)"""
        if not self.current_path:
            return None
        
        try:
            total, used, free = shutil.disk_usage(self.current_path)
            return {
                'total': total,
                'used': used,
                'free': free,
                'path': self.current_path
            }
        except:
            return None