import os
import subprocess

def abrir_explorador_en_ruta(ruta):
    ruta = ruta.rstrip('/')
    
    if os.path.exists(ruta):
        try:
            subprocess.run(['explorer', os.path.normpath(ruta)], check=True)
        except subprocess.CalledProcessError as e:
            print(f"Error al abrir el explorador: {e}")
    else:
        print(f"La ruta no existe o no es accesible: {ruta}")

def manejar_doble_clic(file_path, output_dir, create_folder):
    if file_path:
        # Obtener el nombre de la carpeta de origen
        origin_folder = os.path.basename(os.path.dirname(file_path))
        
        if create_folder:
            # En modo carpeta, la ruta de destino incluye la carpeta de origen
            destination_folder = os.path.join(output_dir, origin_folder)
        else:
            # En modo archivo, usar directamente la carpeta de destino
            destination_folder = output_dir
        
        abrir_explorador_en_ruta(destination_folder)
        
        print(f"Intentando abrir carpeta: {destination_folder}")
        print(f"Modo carpeta: {create_folder}")
        print(f"Carpeta de origen: {origin_folder}")