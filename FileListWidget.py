import json
import math
import os
import sys, datetime
import math
import uuid
from global_functions import apply_consistent_font
import subprocess
from PyQt6.QtCore import Qt, QFileInfo, pyqtSignal, QThread,  QUrl, QTimer, QPropertyAnimation, QEasingCurve, QEvent, QObject, QVariantAnimation, QPoint, QMimeData, QRect, QRectF
from PyQt6.QtGui import QDragEnterEvent, QDropEvent, QIcon, QFont, QWheelEvent, QCursor, QColor, QDrag, QPixmap, QPainter, QPainterPath, QLinearGradient, QPen
from PyQt6.QtWidgets import (QListWidget, QListWidgetItem, QVBoxLayout, QWidget, QGraphicsDropShadowEffect, QHBoxLayout,
                             QDialog, QLabel, QProgressDialog, QApplication)
from PyQt6.QtMultimedia import QSoundEffect
from BARRA_TOTAL import BarraTotal
if getattr(sys, 'frozen', False):
    application_path = sys._MEIPASS
else:
    application_path = os.path.dirname(os.path.abspath(__file__))
def estimate_output_size_ffmpeg(input_file, conversion_params):
        print(f"Estimando tamaño para: {input_file}")
        print(f"Parámetros de conversión: {conversion_params}")
        
        if getattr(sys, 'frozen', False):
            base_path = sys._MEIPASS
        else:
            base_path = os.path.dirname(os.path.abspath(__file__))
        
        ffprobe_path = os.path.join(base_path, 'ffmpeg', 'ffprobe.exe')
        print(f"Ruta de ffprobe: {ffprobe_path}")

        if not os.path.exists(ffprobe_path):
            print(f"ffprobe no encontrado en: {ffprobe_path}")
            return 0
        ffprobe_command = [
            ffprobe_path,
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            input_file
        ]
        result = subprocess.run(ffprobe_command, capture_output=True, text=True)
        video_info = json.loads(result.stdout)
        duration = float(video_info['format']['duration'])
        target_video_bitrate = parse_bitrate(conversion_params.get('bitrate', '0'))
        target_audio_bitrate = parse_bitrate(conversion_params.get('audio_bitrate', '0'))
        target_width = conversion_params.get('width', None)
        target_height = conversion_params.get('height', None)
        target_fps = conversion_params.get('fps', None)
        if target_video_bitrate == 0:
            original_width = int(video_info['streams'][0]['width'])
            original_height = int(video_info['streams'][0]['height'])
            original_fps = eval(video_info['streams'][0]['r_frame_rate'])
            target_width = target_width or original_width
            target_height = target_height or original_height
            target_fps = target_fps or original_fps
            target_video_bitrate = calculate_video_bitrate(target_width, target_height, target_fps)
        total_bitrate = target_video_bitrate + target_audio_bitrate
        estimated_size = (total_bitrate * duration) / (8 * 1024 * 1024)  # Convertir a MB
        estimated_size *= 1.005 # Considerar el overhead del contenedor (asumimos un 0.5% adicional)
        estimated_size = round(estimated_size) # Redondear al MB más cercano
        return estimated_size

def calculate_video_bitrate(width, height, fps):
        pixels = width * height
        if pixels <= 352 * 240:  # SD
            base_bitrate = 1000000
        elif pixels <= 640 * 480:  # 480p
            base_bitrate = 2000000
        elif pixels <= 1280 * 720:  # 720p
            base_bitrate = 4000000
        elif pixels <= 1920 * 1080:  # 1080p
            base_bitrate = 8000000
        else:  # 4K o superior
            base_bitrate = 16000000
        fps_factor = fps / 30.0
        return int(base_bitrate * fps_factor)

def parse_bitrate(bitrate_str):
        if isinstance(bitrate_str, (int, float)):
            return int(bitrate_str)
        if bitrate_str.endswith('k'):
            return int(bitrate_str[:-1]) * 1000
        elif bitrate_str.endswith('M'):
            return int(bitrate_str[:-1]) * 1000000
        else:
            try:
                return int(bitrate_str)
            except ValueError:
                return 0
import uuid

class CustomTooltip(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent, Qt.WindowType.FramelessWindowHint | Qt.WindowType.Tool | Qt.WindowType.WindowStaysOnTopHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setFixedSize(200, 40)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.label = QLabel(self)
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label.setStyleSheet("""
            QLabel {
                background-color: rgba(40, 40, 40, 220);
                color: white;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        
        # Aplicar sombra
        shadow = QGraphicsDropShadowEffect(self.label)
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(2)
        shadow.setColor(QColor(0, 0, 0, 100))
        self.label.setGraphicsEffect(shadow)
        
        layout.addWidget(self.label)
        
        # Timer para ocultar automáticamente
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide)
        self.hide_timer.setSingleShot(True)
    
    def show_message(self, text, duration=2000):
        self.label.setText(text)
        
        # Posicionar en el centro de la ventana padre
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.x() + (parent_rect.width() - self.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - self.height()) // 2
            self.setGeometry(x, y, self.width(), self.height())
        
        self.show()
        self.raise_()
        
        # Ocultar después del tiempo especificado
        self.hide_timer.start(duration)

class CustomListWidgetItem(QWidget):
    def __init__(self, text, file_path=None):
        super().__init__()
        self.file_id = str(uuid.uuid4())  # ID único para cada archivo
        self.file_path = file_path
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # Crear layout horizontal para icono y texto
        self.content_layout = QHBoxLayout()
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(4)  # Espacio reducido entre icono y texto
        
        # Label para el icono (inicialmente vacío)
        self.icon_label = QLabel(self)  # Establecer self como padre
        self.icon_label.setFixedSize(16, 16)  # Tamaño más pequeño para no interferir
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.icon_label.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.icon_label.hide()  # Oculto por defecto
        # Asegurar que el icono esté por encima de la barra de progreso
        self.icon_label.setStyleSheet("background: transparent;")
        
        # Label para el texto
        self.label = QLabel(text)
        self.label.setAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft)
        font = self.label.font()
        font.setBold(True)
        font.setPointSize(9)  # Texto aún más pequeño
        self.label.setFont(font)
        # Asegurar que el label tenga altura fija para centrado perfecto
        self.label.setFixedHeight(20)
        
        # Hacer el fondo del label transparente
        self.label.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # Label para el porcentaje (a la izquierda después del icono de estado)
        self.percentage_label = QLabel()
        self.percentage_label.setAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft)
        font_percentage = self.percentage_label.font()
        font_percentage.setBold(True)
        font_percentage.setPointSize(9)
        self.percentage_label.setFont(font_percentage)
        self.percentage_label.setFixedHeight(20)
        self.percentage_label.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.percentage_label.setStyleSheet("color: rgba(255, 255, 255, 180); background: transparent;")
        self.percentage_label.hide()  # Oculto por defecto
        
        # Botón de cancelar conversión (solo visible durante conversión)
        from botones import crear_boton_cancelar_svg
        self.cancel_button = crear_boton_cancelar_svg(self)
        self.cancel_button.hide()  # Oculto por defecto
        
        # Label para el tiempo restante (pegado al borde derecho)
        self.time_label = QLabel()
        self.time_label.setAlignment(Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignRight)
        font_time = self.time_label.font()
        font_time.setBold(True)
        font_time.setPointSize(9)
        self.time_label.setFont(font_time)
        self.time_label.setFixedHeight(20)
        self.time_label.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.time_label.setStyleSheet("color: rgba(255, 255, 255, 180); background: transparent;")
        self.time_label.hide()  # Oculto por defecto
        
        # Agregar widgets al layout horizontal
        self.content_layout.addWidget(self.icon_label)
        self.content_layout.addWidget(self.percentage_label)
        self.content_layout.addWidget(self.label)
        self.content_layout.addStretch()  # Para empujar el tiempo a la derecha
        self.content_layout.addWidget(self.cancel_button)
        self.content_layout.addWidget(self.time_label)
        
        self.progress_bar = BarraTotal(self)
        self.progress_bar.setBaseColor("#05B8CC")
        self.progress_bar.setValue(0)
        self.progress_bar.setShowPercentage(False)
        self.progress_bar.setBorderRadius(12)  # Más redondeada
        self.progress_bar.setMaximumHeight(20)  # Altura ajustada al nuevo tamaño más pequeño
        self.progress_bar.setMinimumHeight(20)
        # Quitar el fondo gris - hacer transparente
        self.progress_bar.background_fill_color = QColor(0, 0, 0, 0)  # Transparente
        
        # Establecer altura mínima muy compacta
        self.setMinimumHeight(22)  # Altura más compacta para coincidir con QListWidget::item
        self.setMaximumHeight(22)  # Altura fija para consistencia
        
        # Usar layout vertical para mejor control del centrado
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(2, 1, 6, 1)  # Margen izquierdo muy reducido para pegar más al borde
        main_layout.setSpacing(0)
        
        # Crear widget contenedor para el layout horizontal
        content_widget = QWidget()
        content_widget.setLayout(self.content_layout)
        content_widget.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # Centrar el contenido verticalmente con mejor distribución
        main_layout.addStretch(1)
        main_layout.addWidget(content_widget)
        main_layout.addStretch(1)
        
        self.setLayout(main_layout)
        self.progress_bar.setParent(self)
        self.progress_bar.lower()
    def setText(self, text):
        self.label.setText(text)

    def text(self):
        return self.label.text()
    
    def sizeHint(self):
        """Devuelve el tamaño sugerido para el widget"""
        from PyQt6.QtCore import QSize
        return QSize(-1, 22)  # Ancho automático, altura fija de 22px
    
    def setStatusIcon(self, status):
        """Establece el icono según el estado del archivo"""
        from CREAR import icono_CONVIRTIENDO, icono_COMPLETADO, icono_EN_COLA, icono_ERROR, icono_PENDIENTE
        
        print(f"🎯 setStatusIcon llamado con estado: {status}")
        
        if status == "CONVIRTIENDO":
            self.icon_label.setPixmap(icono_CONVIRTIENDO(16).pixmap(16, 16))
            self.icon_label.show()
            print(f"✅ Icono CONVIRTIENDO establecido")
        elif status == "COMPLETADO":
            self.icon_label.setPixmap(icono_COMPLETADO(16).pixmap(16, 16))
            self.icon_label.show()
            print(f"✅ Icono COMPLETADO establecido")
        elif status == "EN_COLA":
            self.icon_label.setPixmap(icono_EN_COLA(16).pixmap(16, 16))
            self.icon_label.show()
            print(f"✅ Icono EN_COLA establecido y mostrado")
        elif status == "ERROR":
            self.icon_label.setPixmap(icono_ERROR(16).pixmap(16, 16))
            self.icon_label.show()
            print(f"✅ Icono ERROR establecido")
        elif status == "PENDIENTE":
            self.icon_label.setPixmap(icono_PENDIENTE(16).pixmap(16, 16))
            self.icon_label.show()
            print(f"✅ Icono PENDIENTE establecido")
        elif status == "CANCELADO":
            # Tratar CANCELADO como PENDIENTE (icono azul)
            self.icon_label.setPixmap(icono_PENDIENTE(16).pixmap(16, 16))
            self.icon_label.show()
            print(f"✅ Icono PENDIENTE establecido (desde CANCELADO)")
        else:
            self.icon_label.hide()
            print(f"❌ Estado desconocido '{status}', ocultando icono")
    
    def hideStatusIcon(self):
        """Oculta el icono de estado"""
        self.icon_label.hide()
    
    def setConversionInfo(self, percentage, remaining_time):
        """Establece la información de conversión (porcentaje y tiempo)"""
        if percentage > 0 and percentage <= 100:
            self.percentage_label.setText(f"{percentage}%")
            self.percentage_label.show()
            if percentage < 100:
                self.time_label.setText(remaining_time)
                self.time_label.show()
                self.cancel_button.show()
            else:
                # Al 100%, mostrar "Completado" en lugar del tiempo y ocultar botón cancelar
                self.time_label.setText("Completado")
                self.time_label.show()
                self.cancel_button.hide()
        else:
            self.hideConversionInfo()
    
    def hideConversionInfo(self):
        """Oculta toda la información de conversión"""
        self.percentage_label.hide()
        self.time_label.hide()
        self.cancel_button.hide()
    
    def setCancelCallback(self, callback):
        """Establece la función callback para el botón de cancelar"""
        # Desconectar conexiones previas para evitar múltiples conexiones
        try:
            self.cancel_button.clicked.disconnect()
        except:
            pass
        self.cancel_button.clicked.connect(callback)
    def resizeEvent(self, event):
        super().resizeEvent(event)
        # Margen izquierdo para dejar espacio al icono, más pegado al borde
        left_margin = 18  # Espacio para el icono (16px) + separación reducida (2px)
        right_margin = 6  # Margen derecho más pequeño para que llegue más al final
        progress_height = self.progress_bar.maximumHeight()
        # Centrar la barra de progreso verticalmente con mejor alineación
        y_position = (self.height() - progress_height) // 2  # Centrado perfecto
        self.progress_bar.setGeometry(
            left_margin,  # Posición X después del icono
            y_position,
            self.width() - left_margin - right_margin,  # Ancho ajustado para llegar más al final
            progress_height
        )






class FileListWidget(QListWidget):
    valorSrtCalculated = pyqtSignal(int)
    totalSizeChanged = pyqtSignal(float)
    conversionNeeded = pyqtSignal()
    itemsReordered = pyqtSignal(list)  # Emitirá una lista de file_paths en el nuevo orden
    itemMoved = pyqtSignal()

    def __init__(self, type):
        super().__init__()
        self.setAcceptDrops(True)
        self.file_data = {}
        self.total_estimated_size = 0
        self.setDragDropMode(QListWidget.DragDropMode.DragDrop)  # Permitir drag & drop
        self.setDefaultDropAction(Qt.DropAction.CopyAction)  # Acción predeterminada: copiar
        self.model().rowsMoved.connect(self.on_rows_moved)
        
        # Variables para drag & drop
        self.drag_start_position = None
        self.dragging_completed_files = False
        
        # Separación mínima de 1 pixel entre archivos
        self.setSpacing(1)
        
        # Crear tooltip personalizado
        self.custom_tooltip = CustomTooltip(self.window())

    def wheelEvent(self, event: QWheelEvent):
        if self.selectedItems():
            delta = event.angleDelta().y()
            if delta > 0:
                self.move_items_up()
            elif delta < 0:
                self.move_items_down()
            event.accept()
        else:
            super().wheelEvent(event)

    def move_items_up(self):
        self.move_items(-1)

    def move_items_down(self):
        self.move_items(1)

    def move_items(self, direction):
        rows = sorted([self.row(item) for item in self.selectedItems()])
        if direction == -1 and rows[0] == 0:
            return
        if direction == 1 and rows[-1] == self.count() - 1:
            return

        # Verificar si algún elemento seleccionado puede ser movido
        for row in rows:
            item = self.item(row)
            can_move = self.can_item_be_moved(item)
            if not can_move:
                print(f"Movimiento bloqueado en fila {row}")
                return  # No permitir el movimiento si algún elemento no puede moverse

        # Guardar información completa de los widgets antes del movimiento
        widgets_info = []
        for row in rows:
            item = self.item(row)
            widget = self.itemWidget(item)
            if isinstance(widget, CustomListWidgetItem):
                # Determinar el estado actual del archivo
                file_path = item.data(Qt.ItemDataRole.UserRole)
                current_status = "PENDIENTE"  # Por defecto
                
                # Verificar en orden de prioridad
                if hasattr(widget, 'percentage_label') and widget.percentage_label.isVisible():
                    current_status = "CONVIRTIENDO"
                    print(f"Estado detectado CONVIRTIENDO para {os.path.basename(file_path)}")
                elif self.is_item_completed(item):
                    current_status = "COMPLETADO"
                    print(f"Estado detectado COMPLETADO para {os.path.basename(file_path)}")
                elif hasattr(widget, 'icon_label') and widget.icon_label.isVisible():
                    # Verificar si es realmente un archivo completado por el icono
                    pixmap = widget.icon_label.pixmap()
                    if pixmap and not pixmap.isNull():
                        # Intentar determinar si es icono de completado vs en cola
                        # basándose en file_data
                        if file_path in self.file_data and self.file_data[file_path].get('converted', False):
                            current_status = "COMPLETADO"
                            print(f"Estado detectado COMPLETADO (por file_data) para {os.path.basename(file_path)}")
                        else:
                            current_status = "EN_COLA"
                            print(f"Estado detectado EN_COLA para {os.path.basename(file_path)}")
                    else:
                        current_status = "EN_COLA"
                        print(f"Estado detectado EN_COLA (sin pixmap) para {os.path.basename(file_path)}")
                else:
                    print(f"Estado detectado PENDIENTE para {os.path.basename(file_path)}")
                
                # Guardar estado completo del widget
                widget_state = {
                    'row': row,
                    'text': widget.text(),
                    'file_path': file_path,
                    'current_status': current_status,  # Estado actual determinado
                    'has_icon': hasattr(widget, 'icon_label') and widget.icon_label.isVisible(),
                    'icon_pixmap': widget.icon_label.pixmap() if hasattr(widget, 'icon_label') and widget.icon_label.isVisible() else None,
                    'is_converting': hasattr(widget, 'percentage_label') and widget.percentage_label.isVisible(),
                    'percentage': widget.percentage_label.text() if hasattr(widget, 'percentage_label') and widget.percentage_label.isVisible() else None,
                    'time_text': widget.time_label.text() if hasattr(widget, 'time_label') and widget.time_label.isVisible() else None,
                    'progress_value': widget.progress_bar.value() if hasattr(widget, 'progress_bar') else 0
                }
                widgets_info.append(widget_state)

        for i, row in enumerate(rows if direction == -1 else reversed(rows)):
            item = self.takeItem(row)
            new_row = row + direction
            self.insertItem(new_row, item)

            # Recrear el widget con la información guardada
            widget_info = widgets_info[i if direction == -1 else len(widgets_info) - 1 - i]
            file_path = widget_info['file_path']
            
            # Crear nuevo widget personalizado
            custom_widget = CustomListWidgetItem(widget_info['text'], file_path)
            self.setItemWidget(item, custom_widget)
            
            # Asegurar que el item mantenga su file_id original
            original_file_id = item.data(Qt.ItemDataRole.UserRole + 3)
            if original_file_id:
                item.setData(Qt.ItemDataRole.UserRole + 3, original_file_id)
            
            # Restaurar el estado usando el estado guardado
            status_to_restore = widget_info['current_status']
            print(f"Moviendo {os.path.basename(file_path)}: estado guardado = {status_to_restore}")
            
            if status_to_restore == "CONVIRTIENDO" and widget_info['is_converting']:
                # Restaurar información de conversión
                if widget_info['percentage'] and widget_info['time_text']:
                    percentage = int(widget_info['percentage'].replace('%', ''))
                    custom_widget.setConversionInfo(percentage, widget_info['time_text'])
                    custom_widget.progress_bar.setValue(widget_info['progress_value'])
                    custom_widget.progress_bar.show()
                custom_widget.setStatusIcon("CONVIRTIENDO")
                print(f"✓ Restaurado estado CONVIRTIENDO para {os.path.basename(file_path)}")
            else:
                # Restaurar icono según el estado guardado
                custom_widget.setStatusIcon(status_to_restore)
                print(f"✓ Restaurado estado {status_to_restore} para {os.path.basename(file_path)}")
                
                # Verificación adicional para archivos completados
                if status_to_restore == "COMPLETADO":
                    # Asegurar que file_data esté actualizado
                    if file_path in self.file_data:
                        self.file_data[file_path]['converted'] = True
                        print(f"✓ Marcado como convertido en file_data: {os.path.basename(file_path)}")

            if file_path in self.file_data:
                self.file_data[file_path]['row'] = new_row
                print(f"Actualizada posición de {os.path.basename(file_path)} a fila {new_row}")

        self.clearSelection()
        for row in range(rows[0] + direction, rows[-1] + direction + 1):
            item = self.item(row)
            if item:
                item.setSelected(True)

        self.itemMoved.emit()
        self.itemsReordered.emit(self.get_waiting_files_order())
        
        # IMPORTANTE: Después de mover archivos, reconstruir la cola y verificar conversiones
        if hasattr(self.window(), 'rebuild_job_queue_after_move'):
            print("Reconstruyendo cola después del movimiento")
            self.window().rebuild_job_queue_after_move()
        elif hasattr(self.window(), 'check_and_start_conversions'):
            print("Reactivando conversiones después del movimiento")
            self.window().check_and_start_conversions()

    def startDrag(self, supportedActions):
        """Inicia la operación de arrastre con los elementos seleccionados (estilo ZETACOPY)"""
        selected_items = self.selectedItems()
        if not selected_items:
            return
            
        # Crear un objeto QMimeData para transferir los datos
        mime_data = QMimeData()
        
        # Lista de URLs para los archivos seleccionados
        urls = []
        
        # Calcular el tamaño total de los archivos seleccionados
        total_size = 0
        converted_count = 0
        pending_count = 0
        
        # Añadir cada archivo seleccionado a la lista de URLs
        for item in selected_items:
            file_path = item.data(Qt.ItemDataRole.UserRole)
            if not file_path:
                continue
                
            # Verificar si el archivo existe
            if not os.path.exists(file_path):
                print(f"Error: La ruta no existe: {file_path}")
                continue
                
            # Crear la URL para el arrastre
            url = QUrl.fromLocalFile(file_path)
            urls.append(url)
            
            # Verificar si el archivo está convertido
            if file_path in self.file_data and self.file_data[file_path].get('converted', False):
                converted_count += 1
                # Para archivos convertidos, buscar el archivo de salida
                try:
                    if hasattr(self.window(), 'get_output_dir') and hasattr(self.window(), 'parametros_conversion'):
                        output_dir = self.window().get_output_dir(file_path)
                        extension = self.window().parametros_conversion.get('extension', '')
                        file_name = os.path.splitext(os.path.basename(file_path))[0]
                        output_file = os.path.join(output_dir, f"{file_name}{extension}")
                        if os.path.exists(output_file):
                            total_size += os.path.getsize(output_file)
                        elif os.path.exists(file_path):
                            total_size += os.path.getsize(file_path)
                    elif os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
                except:
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
            else:
                pending_count += 1
                # Para archivos pendientes, usar el archivo original
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)
        
        # Si tenemos URLs, las añadimos al objeto QMimeData
        if urls:
            mime_data.setUrls(urls)
            
            # Añadir formatos adicionales para mejorar la compatibilidad
            if len(urls) == 1:
                # Para un solo archivo, añadir su ruta como texto
                mime_data.setText(urls[0].toLocalFile())
            else:
                # Para múltiples archivos, añadir una lista de rutas
                text_paths = "\n".join([url.toLocalFile() for url in urls])
                mime_data.setText(text_paths)
            
            # Iniciar la operación de arrastre
            drag = QDrag(self)
            drag.setMimeData(mime_data)
            
            # Crear un pixmap personalizado para el cursor de arrastre
            num_files = len(urls)
            file_size = self.format_size(total_size)
            
            if num_files == 1:
                # Para un solo archivo, mostrar su nombre y tamaño
                file_name = os.path.basename(urls[0].toLocalFile())
                
                # Crear un pixmap más pequeño con estilo TOOLTIP_APARIENCIA
                pixmap = QPixmap(180, 35)
                pixmap.fill(Qt.GlobalColor.transparent)
                
                painter = QPainter(pixmap)
                painter.setRenderHint(QPainter.RenderHint.Antialiasing)
                
                # Crear path con bordes redondeados (estilo TOOLTIP_APARIENCIA)
                from PyQt6.QtGui import QPainterPath, QLinearGradient
                path = QPainterPath()
                rect = QRectF(pixmap.rect()).adjusted(1, 1, -1, -1)
                radius = 8.0
                path.addRoundedRect(rect, radius, radius)
                
                # Dibujar sombra sutil
                painter.setPen(Qt.PenStyle.NoPen)
                for i in range(2):
                    shadow_path = QPainterPath()
                    shadow_rect = rect.adjusted(-i, -i, i, i)
                    shadow_path.addRoundedRect(shadow_rect, radius, radius)
                    painter.setBrush(QColor(0, 0, 0, 15 - i * 5))
                    painter.drawPath(shadow_path)
                
                # Dibujar fondo con gradiente (estilo TOOLTIP_APARIENCIA)
                gradient = QLinearGradient(0, 0, 0, pixmap.height())
                gradient.setColorAt(0.0, QColor(44, 44, 44, 245))
                gradient.setColorAt(1.0, QColor(32, 32, 32, 245))
                painter.setBrush(gradient)
                painter.drawPath(path)
                
                # Dibujar borde sutil
                from PyQt6.QtGui import QPen
                pen = QPen()
                pen.setColor(QColor(255, 255, 255, 20))
                pen.setWidth(1)
                painter.setPen(pen)
                painter.drawPath(path)
                
                # Dibujar el texto del archivo (más compacto)
                painter.setPen(QColor(255, 255, 255))
                font = QFont("Segoe UI", 8)
                font.setWeight(QFont.Weight.Normal)
                painter.setFont(font)
                
                # Texto en una sola línea más compacta
                text_rect = QRect(8, 8, pixmap.width() - 16, pixmap.height() - 16)
                painter.drawText(text_rect, Qt.AlignmentFlag.AlignCenter, f"{file_name} • {file_size}")
                
                painter.end()
                
                # Establecer el pixmap como cursor
                drag.setPixmap(pixmap)
                drag.setHotSpot(QPoint(8, 8))
            else:
                # Para múltiples archivos, mostrar el número y el tamaño total
                pixmap = QPixmap(160, 40)
                pixmap.fill(Qt.GlobalColor.transparent)
                
                painter = QPainter(pixmap)
                painter.setRenderHint(QPainter.RenderHint.Antialiasing)
                
                # Crear path con bordes redondeados (estilo TOOLTIP_APARIENCIA)
                from PyQt6.QtGui import QPainterPath, QLinearGradient
                path = QPainterPath()
                rect = QRectF(pixmap.rect()).adjusted(1, 1, -1, -1)
                radius = 8.0
                path.addRoundedRect(rect, radius, radius)
                
                # Dibujar sombra sutil
                painter.setPen(Qt.PenStyle.NoPen)
                for i in range(2):
                    shadow_path = QPainterPath()
                    shadow_rect = rect.adjusted(-i, -i, i, i)
                    shadow_path.addRoundedRect(shadow_rect, radius, radius)
                    painter.setBrush(QColor(0, 0, 0, 15 - i * 5))
                    painter.drawPath(shadow_path)
                
                # Dibujar fondo con gradiente (estilo TOOLTIP_APARIENCIA)
                gradient = QLinearGradient(0, 0, 0, pixmap.height())
                gradient.setColorAt(0.0, QColor(44, 44, 44, 245))
                gradient.setColorAt(1.0, QColor(32, 32, 32, 245))
                painter.setBrush(gradient)
                painter.drawPath(path)
                
                # Dibujar borde sutil
                from PyQt6.QtGui import QPen
                pen = QPen()
                pen.setColor(QColor(255, 255, 255, 20))
                pen.setWidth(1)
                painter.setPen(pen)
                painter.drawPath(path)
                
                # Dibujar el texto (más compacto)
                painter.setPen(QColor(255, 255, 255))
                font = QFont("Segoe UI", 8)
                font.setWeight(QFont.Weight.Normal)
                painter.setFont(font)
                
                # Línea superior: número de archivos
                files_rect = QRect(8, 6, pixmap.width() - 16, 14)
                painter.drawText(files_rect, Qt.AlignmentFlag.AlignCenter, f"{num_files} archivos")
                
                # Línea inferior: tamaño total
                size_rect = QRect(8, 20, pixmap.width() - 16, 14)
                painter.setPen(QColor(180, 180, 180))  # Color más suave para el tamaño
                painter.drawText(size_rect, Qt.AlignmentFlag.AlignCenter, f"{file_size}")
                
                painter.end()
                
                # Establecer el pixmap como cursor
                drag.setPixmap(pixmap)
                drag.setHotSpot(QPoint(8, 8))
                
            # Ejecutar la operación de arrastre con acciones explícitas
            actions = Qt.DropAction.CopyAction | Qt.DropAction.MoveAction | Qt.DropAction.LinkAction
            result = drag.exec(actions)
            
            print(f"Arrastre completado con resultado: {result}")

    def format_size(self, size_bytes):
        """Formatea un tamaño en bytes a una representación legible"""
        if size_bytes < 0:
            return "0 B"
        
        # Definir unidades
        units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
        
        # Calcular la unidad adecuada
        i = 0
        while size_bytes >= 1024 and i < len(units) - 1:
            size_bytes /= 1024.0
            i += 1
        
        # Formatear con 2 decimales si es necesario
        if i > 0:
            return f"{size_bytes:.2f} {units[i]}"
        else:
            return f"{size_bytes} {units[i]}"



    def dragEnterEvent(self, event: QDragEnterEvent):
        """Maneja cuando se arrastra algo sobre el widget"""
        # Si es un arrastre interno (desde la misma vista), rechazarlo para evitar duplicados
        if event.source() == self:
            event.ignore()
            return
        
        # Aceptar solo archivos externos
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()

    def dragMoveEvent(self, event):
        """Maneja el movimiento durante el arrastre"""
        # Si es arrastre interno (desde la misma vista), rechazar
        if event.source() == self:
            event.ignore()
            return
        
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event: QDropEvent):
        """Maneja cuando se suelta algo en el widget"""
        # Si es arrastre interno (desde la misma vista), rechazar para evitar duplicados
        if event.source() == self:
            event.ignore()
            return
        
        # Solo procesar archivos externos
        if event.mimeData().hasUrls():
            file_paths = []
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    file_paths.append(url.toLocalFile())
            
            if file_paths:
                # Procesar archivos externos normalmente
                self.process_dropped_files(file_paths)
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()

    def mouseReleaseEvent(self, event):
        """Resetea el estado de arrastre interno"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_internal_drag = False
            self.drag_start_position = None
        super().mouseReleaseEvent(event)

    def process_dropped_files(self, file_paths):
        """Procesa archivos soltados desde fuera del widget"""
        # Este método debería ser implementado según la lógica existente
        # para procesar archivos nuevos
        if hasattr(self.window(), 'add_files_to_list'):
            self.window().add_files_to_list(file_paths)
        elif hasattr(self.window(), 'process_files'):
            self.window().process_files(file_paths)

    def on_rows_moved(self, parent, start, end, destination, row):
        """Maneja cuando las filas son movidas internamente"""
        new_order = self.get_waiting_files_order()
        self.itemsReordered.emit(new_order)

    def get_waiting_files_order(self):
        """Obtiene el orden actual de archivos en espera"""
        waiting_files = []
        for i in range(self.count()):
            item = self.item(i)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            if file_path and file_path in self.file_data:
                if not self.file_data[file_path].get('converted', False):
                    waiting_files.append(file_path)
        return waiting_files

    def is_item_converting(self, item):
        """Verifica si un elemento se está convirtiendo"""
        widget = self.itemWidget(item)
        if isinstance(widget, CustomListWidgetItem):
            return hasattr(widget, 'percentage_label') and widget.percentage_label.isVisible()
        return False
    
    def is_item_completed(self, item):
        """Verifica si un elemento está completado"""
        widget = self.itemWidget(item)
        if isinstance(widget, CustomListWidgetItem):
            file_path = item.data(Qt.ItemDataRole.UserRole)
            
            # Verificar múltiples indicadores de completado
            if file_path in self.file_data and self.file_data[file_path].get('converted', False):
                return True
            
            # También verificar si el archivo existe en el destino
            if hasattr(self.window(), 'get_output_dir') and hasattr(self.window(), 'parametros_conversion'):
                try:
                    output_dir = self.window().get_output_dir(file_path)
                    extension = self.window().parametros_conversion.get('extension', '')
                    file_name = os.path.splitext(os.path.basename(file_path))[0]
                    output_file = os.path.join(output_dir, f"{file_name}{extension}")
                    
                    if os.path.exists(output_file):
                        # Si el archivo existe, marcarlo como convertido en file_data
                        if file_path in self.file_data:
                            self.file_data[file_path]['converted'] = True
                        return True
                except:
                    pass
        
        return False
    
    def is_item_in_queue(self, item):
        """Verifica si un elemento está en cola (puede moverse)"""
        widget = self.itemWidget(item)
        if isinstance(widget, CustomListWidgetItem):
            file_path = item.data(Qt.ItemDataRole.UserRole)
            # Un archivo está en cola si:
            # 1. No está en conversión (sin porcentaje visible)
            # 2. No está completado (converted = False)
            # 3. Tiene icono visible (está en la lista para convertir)
            if not self.is_item_converting(item) and not self.is_item_completed(item):
                if hasattr(widget, 'icon_label') and widget.icon_label.isVisible():
                    return True
        return False
    
    def can_item_be_moved(self, item):
        """Verifica si un elemento puede ser movido"""
        if not item:
            print("No se puede mover: item es None")
            return False
            
        widget = self.itemWidget(item)
        if not isinstance(widget, CustomListWidgetItem):
            print("No se puede mover: no es CustomListWidgetItem")
            return False
            
        file_path = item.data(Qt.ItemDataRole.UserRole)
        if not file_path:
            print("No se puede mover: no tiene file_path")
            return False
        
        # Verificar si está en conversión (tiene porcentaje visible)
        is_converting = hasattr(widget, 'percentage_label') and widget.percentage_label.isVisible()
        if is_converting:
            print(f"No se puede mover {os.path.basename(file_path)}: está en conversión")
            return False
        
        # Verificar si está completado
        is_completed = file_path in self.file_data and self.file_data[file_path].get('converted', False)
        if is_completed:
            print(f"No se puede mover {os.path.basename(file_path)}: está completado")
            return False
        
        # Si llegamos aquí, se puede mover
        return True
    
    def get_item_status(self, item):
        """Determina el estado correcto de un archivo"""
        widget = self.itemWidget(item)
        if isinstance(widget, CustomListWidgetItem):
            file_path = item.data(Qt.ItemDataRole.UserRole)
            
            # Si está en conversión
            if self.is_item_converting(item):
                return "CONVIRTIENDO"
            
            # Si está completado
            if self.is_item_completed(item):
                return "COMPLETADO"
            
            # Si está en file_data pero no convertido, está en cola
            if file_path in self.file_data and not self.file_data[file_path].get('converted', False):
                return "EN_COLA"
            
            # Estado por defecto para archivos recién agregados
            return "PENDIENTE"
        
        return "PENDIENTE"

    def get_next_file_to_convert(self):
        """Obtiene el siguiente archivo a convertir, priorizando los que no existen en el destino"""
        for i in range(self.count()):
            item = self.item(i)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            
            if file_path in self.file_data:
                # Verificar si el archivo ya existe en el destino
                output_dir = self.file_data[file_path].get('output_dir', '')
                extension = self.cargar_extension_desde_configuracion(self.window().settingsComboBox.currentText())
                
                # Construir la ruta del archivo de salida
                file_name = os.path.basename(file_path)
                file_name_without_ext = os.path.splitext(file_name)[0]
                output_file = os.path.join(output_dir, f"{file_name_without_ext}{extension}")
                
                # Si el archivo no existe en el destino y no está marcado como convertido
                if not os.path.exists(output_file) and not self.file_data[file_path].get('converted', False):
                    print(f"Archivo no encontrado en destino, priorizando: {file_path}")
                    return file_path, self.file_data[file_path]
        
        # Si todos los archivos existen en el destino, buscar el siguiente no convertido
        for i in range(self.count()):
            item = self.item(i)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            if file_path in self.file_data and not self.file_data[file_path].get('converted', False):
                return file_path, self.file_data[file_path]
        
        return None, None

    def mark_file_as_converted(self, file_path):
        if file_path in self.file_data:
            self.file_data[file_path]['converted'] = True

    def reset_conversion_status(self):
        """Reinicia el estado de conversión de todos los archivos y verifica su existencia en el destino"""
        for i in range(self.count()):
            item = self.item(i)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            
            if file_path in self.file_data:
                # Verificar si el archivo existe en el destino
                output_dir = self.file_data[file_path].get('output_dir', '')
                extension = self.cargar_extension_desde_configuracion(self.window().settingsComboBox.currentText())
                
                # Construir la ruta del archivo de salida
                file_name = os.path.basename(file_path)
                file_name_without_ext = os.path.splitext(file_name)[0]
                output_file = os.path.join(output_dir, f"{file_name_without_ext}{extension}")
                
                # Marcar como no convertido si no existe en el destino
                if not os.path.exists(output_file):
                    self.file_data[file_path]['converted'] = False
                    
                    # Actualizar el texto del elemento para mostrar que necesita conversión
                    if "Detenido" in item.text() or "Convirtiendo" in item.text() or "Completado" in item.text():
                        nuevo_texto = item.text().replace("Detenido", "Listo").replace("Convirtiendo", "Listo").replace("Completado", "Listo")
                        item.setText(nuevo_texto)
                else:
                    # El archivo existe, verificar si está completo
                    try:
                        # Intentar abrir el archivo para verificar que no esté corrupto
                        with open(output_file, 'rb') as f:
                            # Leer los últimos bytes para verificar que el archivo esté completo
                            f.seek(max(0, os.path.getsize(output_file) - 1024))
                            f.read(1024)
                        
                        # Si llegamos aquí, el archivo parece estar completo
                        self.file_data[file_path]['converted'] = True
                    except:
                        # Si hay un error al leer el archivo, probablemente esté corrupto
                        self.file_data[file_path]['converted'] = False
                        
                        # Actualizar el texto del elemento
                        if "Detenido" in item.text() or "Convirtiendo" in item.text() or "Completado" in item.text():
                            nuevo_texto = item.text().replace("Detenido", "Listo").replace("Convirtiendo", "Listo").replace("Completado", "Listo")
                            item.setText(nuevo_texto)

    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dragMoveEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
    
    def dragLeaveEvent(self, event):
        """Oculta el tooltip cuando el arrastre sale del widget"""
        self.custom_tooltip.hide()
        super().dragLeaveEvent(event)

    def dropEvent(self, event: QDropEvent):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            urls = [url.toLocalFile() for url in event.mimeData().urls()]
            file_paths = []
            for url in urls:
                if os.path.isdir(url):
                    for root, dirs, files in os.walk(url):
                        for file in files:
                            file_path = os.path.join(root, file)
                            extension = os.path.splitext(file_path)[1]
                            if extension.lower() in ['.mp4', '.avi', '.mkv', '.flv', '.vob', '.mpg', '.mpeg']:
                                file_paths.append(file_path)
                else:
                    extension = os.path.splitext(url)[1]
                    if extension.lower() in ['.mp4', '.avi', '.mkv', '.flv', '.vob', '.mpg', '.mpeg']:
                        file_paths.append(url)

            existing_files = set(self.item(i).data(Qt.ItemDataRole.UserRole) for i in range(self.count()))
            new_file_paths = [path for path in file_paths if path not in existing_files]

            if new_file_paths:
                self.load_files(new_file_paths)
            else:
                # Si no hay archivos nuevos, mostrar mensaje
                if len(file_paths) > 0:
                    duplicates = len(file_paths) - len(new_file_paths)
                    if duplicates > 0:
                        self.custom_tooltip.show_message(f"Advertencia: {duplicates} archivo{'s' if duplicates != 1 else ''} ya existe{'n' if duplicates != 1 else ''}", 2000)
        else:
            super().dropEvent(event)
        
        self.itemMoved.emit()
        self.itemsReordered.emit(self.get_waiting_files_order())

    def load_files(self, file_paths):
        # Mostrar tooltip con cantidad de archivos cargados
        self.custom_tooltip.show_message(f"✓ {len(file_paths)} archivo{'s' if len(file_paths) != 1 else ''} agregado{'s' if len(file_paths) != 1 else ''}", 2000)
        
        # Cargar archivos directamente sin barra de progreso
        output_dir = self.window().output_dir
        
        for file_path in file_paths:
            self.add_file_to_list(file_path, output_dir, 0, "00:00:00", False)

    def cargar_configuracion_conversion(self):
        nombre_archivo_configuracion = self.window().settingsComboBox.currentText()
        try:
            if getattr(sys, 'frozen', False):
                base_path = os.path.dirname(sys.executable)
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))
            
            config_path = os.path.join(base_path, f'{nombre_archivo_configuracion}.json')
            
            if os.path.exists(config_path):
                with open(config_path, 'r') as config_file:
                    return json.load(config_file)
            else:
                print(f"Archivo de configuración no encontrado: {config_path}")
                return {}
        except Exception as e:
            print(f"Error al cargar la configuración: {e}")
            return {}

    def add_file_to_list(self, file_path, output_dir, estimated_size, duration_str, is_error):
        print(f"Archivo cargado: {file_path}")
        file_name = os.path.basename(file_path)
        file_name_to_display = os.path.splitext(file_name)[0]
        
        if not any(self.item(i).data(Qt.ItemDataRole.UserRole) == file_path for i in range(self.count())):
            # Crear una instancia de CustomListWidgetItem
            custom_widget = CustomListWidgetItem(file_name_to_display, file_path)
            custom_widget.setStatusIcon("PENDIENTE")  # Mostrar icono azul para archivos recién arrastrados
            apply_consistent_font(custom_widget.label) # Aplicar fuente al label dentro del widget

            # Crear un QListWidgetItem y asociar el custom_widget
            item = QListWidgetItem(self)
            # Establecer el tamaño exacto para que coincida con el CSS
            from PyQt6.QtCore import QSize
            item.setSizeHint(QSize(-1, 23))  # 22px del widget + 1px de spacing
            self.addItem(item)
            self.setItemWidget(item, custom_widget) # Asignar el widget personalizado

            file_id = str(uuid.uuid4())  # Generar ID único
            item.setData(Qt.ItemDataRole.UserRole, file_path)
            item.setData(Qt.ItemDataRole.UserRole + 1, estimated_size)
            item.setData(Qt.ItemDataRole.UserRole + 2, duration_str)
            item.setData(Qt.ItemDataRole.UserRole + 3, file_id)  # Guardar el ID único
            
            self.file_data[file_path] = {
                'file_id': file_id,
                'output_dir': output_dir,
                'estimated_size': estimated_size,
                'duration': duration_str,
                'row': self.count() - 1,
                'needs_conversion': True
            }



    def update_list_visibility(self):
        for i in range(self.count()):
            item = self.item(i)
            if "✔️" in item.text():  # Verifica si el archivo está completado
                item.setHidden(False)
    def cargar_extension_desde_configuracion(self, nombre_archivo_configuracion):
        try:
            with open(f'{nombre_archivo_configuracion}.json', 'r') as config_file:
                configuracion = json.load(config_file)
                return configuracion['extension']
        except Exception as e:
            print(f"Error al cargar la configuración: {e}")
            return '.default'

    def clear(self):
        super().clear()
        self.total_estimated_size = 0
        self.totalSizeChanged.emit(self.total_estimated_size)

    def on_rows_moved(self, parent, start, end, destination, row):
        new_order = self.get_waiting_files_order()
        self.itemsReordered.emit(new_order)

    def get_waiting_files_order(self):
        return [self.item(i).data(Qt.ItemDataRole.UserRole) for i in range(self.count()) if "▶" in self.item(i).text()]

    def is_item_waiting(self, item):
        return "▶" in item.text()
    
