from PyQt6.QtWidgets import QFileDialog

def seleccionar_destino(self):
    selected_dir = QFileDialog.getExistingDirectory(self, "Selecciona el directorio de destino")
    if selected_dir:
        # Asegúrate de que la ruta del directorio termine con una barra
        if not selected_dir.endswith('/'):
            selected_dir += '/'
        self.output_dir = selected_dir  # Actualiza la variable output_dir con la ruta seleccionada.
        if self.output_dir not in [self.destinationComboBox.itemText(i) for i in range(self.destinationComboBox.count())]:
            self.destinationComboBox.addItem(self.output_dir)
        self.destinationComboBox.setCurrentIndex(self.destinationComboBox.findText(self.output_dir))
    else:
        print("No se seleccionó ningún directorio.")

def on_destinationComboBox_activated(self, text):
    if text == "...":
        self.seleccionar_destino()
    else:
        self.output_dir = text