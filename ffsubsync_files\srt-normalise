#!C:\Users\<USER>\venv\Scripts\python.exe

"""Takes a badly formatted SRT file and outputs a strictly valid one."""

import srt_tools.utils
import logging

log = logging.getLogger(__name__)


def main():
    examples = {"Normalise a subtitle": "srt normalise -i bad.srt -o good.srt"}

    args = srt_tools.utils.basic_parser(
        description=__doc__, examples=examples, hide_no_strict=True
    ).parse_args()
    logging.basicConfig(level=args.log_level)
    srt_tools.utils.set_basic_args(args)
    output = srt_tools.utils.compose_suggest_on_fail(args.input, strict=args.strict)

    try:
        args.output.write(output)
    except (UnicodeEncodeError, TypeError):  # Python 2 fallback
        args.output.write(output.encode(args.encoding))


if __name__ == "__main__":  # pragma: no cover
    main()
