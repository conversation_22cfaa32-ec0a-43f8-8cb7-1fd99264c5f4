from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QGraphicsDropShadowEffect, QPushButton
from PyQt6.QtGui import QIcon, QColor, QPainter
from PyQt6.QtCore import Qt, QSize, QPropertyAnimation, pyqtProperty, QRectF
import os
import json

def get_icon_path(icon_name):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    icon_path = os.path.join(current_dir, 'ICONOS', icon_name)
    if not os.path.exists(icon_path):
        print(f"Advertencia: No se pudo encontrar el icono: {icon_path}")
    return icon_path

class AnimatedButton(QPushButton):
    def __init__(self, icon_name, tooltip, click_handler=None, parent=None):
        super().__init__(parent)
        self.setStyleSheet("QPushButton {border: none; background: transparent;}")
        self._icon_size = 25
        self.setFixedSize(35, 35)
        icon_path = get_icon_path(icon_name)
        self.icon = QIcon(icon_path)
        self.setMouseTracking(True)
        self.setToolTip(tooltip)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        if click_handler:
            self.clicked.connect(click_handler)
        
        self.add_shadow_effect(Qt.GlobalColor.white)

    def add_shadow_effect(self, color):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(color)
        self.setGraphicsEffect(shadow)

    @pyqtProperty(float)
    def icon_size(self):
        return self._icon_size

    @icon_size.setter
    def icon_size(self, size):
        self._icon_size = size
        self.update()

    def enterEvent(self, event):
        self.animate_icon_size(30)

    def leaveEvent(self, event):
        self.animate_icon_size(25)

    def animate_icon_size(self, target_size):
        self.animation = QPropertyAnimation(self, b"icon_size")
        self.animation.setDuration(100)
        self.animation.setStartValue(self.icon_size)
        self.animation.setEndValue(target_size)
        self.animation.start()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)

        icon_rect = QRectF((self.width() - self._icon_size) / 2,
                           (self.height() - self._icon_size) / 2,
                           self._icon_size, self._icon_size)
        self.icon.paint(painter, icon_rect.toRect())

class CerrarAppDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setModal(True)

        # Crear un QFrame para el contenido
        self.content_frame = QFrame(self)
        self.content_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.content_frame.setFrameShadow(QFrame.Shadow.Raised)
        self.content_frame.setStyleSheet("""
            QFrame {
                background-color: #2D2D2C;
                border-radius: 15px;
                border: 1px solid gray;
            }
        """)
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(15)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(0)
        self.shadow_effect.setColor(Qt.GlobalColor.white)
        self.content_frame.setGraphicsEffect(self.shadow_effect)

        # Layout del contenido
        content_layout = QVBoxLayout(self.content_frame)
        content_layout.setContentsMargins(15, 15, 15, 15)

        self.label = QLabel("¿CERRAR APLICACIÓN?")
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label.setStyleSheet("color: white; font-size: 14px; font-weight: bold; border: none;")
        content_layout.addWidget(self.label)

        button_layout = QHBoxLayout()
        button_layout.setSpacing(5)
        content_layout.addLayout(button_layout)

        self.btn_yes = AnimatedButton('YES.png', "Sí, cerrar la aplicación", self.accept)
        self.btn_no = AnimatedButton('NO.png', "No, continuar", self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.btn_yes)
        button_layout.addWidget(self.btn_no)
        button_layout.addStretch()

        # Establecer el layout principal del QDialog
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(self.content_frame)

def gestionar_cierre_aplicacion(main_window, event, output_dir, conversion_limit, settings_name, create_folder, processes, is_converting):
    with open('settings.txt', 'w') as f:  # Guardar configuraciones
        json.dump({
            'output_dir': output_dir,
            'conversion_limit': conversion_limit,
            'settings_name': settings_name,
            'create_folder': create_folder,
            'icon_name': 'FOLDER.png' if create_folder else 'ARCHIVO.png'
        }, f)
    
    try:
        # Cerrar el monitor de temperatura si existe
        if hasattr(main_window, 'temp_monitor'):
            print("Cerrando monitor de temperatura...")
            main_window.temp_monitor.cerrar()
        
        conversions_in_progress = any(process.poll() is None for process in processes.values()) or is_converting
        if conversions_in_progress:
            if mostrar_dialogo_cierre(main_window):
                main_window.on_stopButton_clicked()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
    except Exception as e:
        print(f"Error al cerrar la aplicación: {e}")
        event.accept()  # Cerrar la aplicación incluso si hay un error

def mostrar_dialogo_cierre(parent=None):
    dialogo = CerrarAppDialog(parent)
    resultado = dialogo.exec()
    return resultado == QDialog.DialogCode.Accepted