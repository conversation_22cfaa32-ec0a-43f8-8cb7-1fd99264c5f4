def calcular(tamano_original, tamano_convertido, tiempo):
    # Calcula el porcentaje de conversión en base al tamaño de los archivos y el tiempo transcurrido
    # Asume que la velocidad de conversión es constante
    if tamano_original == 0 or tiempo == 0:
        return 0
    else:
        velocidad = tamano_convertido / tiempo # Bytes por segundo
        tiempo_total = tamano_original / velocidad # Segundos
        porcentaje = (tiempo / tiempo_total) * 100 # Porcentaje
        return porcentaje
