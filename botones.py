from PyQt6.QtWidgets import QPushButton, QGraphicsDropShadowEffect
from PyQt6.QtGui import QIcon, QCursor, QPainter, QColor
from PyQt6.QtCore import QSize, Qt, QEvent, Q<PERSON><PERSON>tyAnimation, pyqtProperty, QRectF, QPoint
from CREAR import boton_PLAY, boton_STOP, boton_AJUSTES, boton_BORRAR
from TOOLTIP_APARIENCIA import showTooltipAtWidget
import os
import sys

def get_icon_path(icon_name):
    if getattr(sys, 'frozen', False):
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(os.path.abspath(__file__))
    
    icon_path = os.path.join(base_path, 'ICONOS', icon_name)
    
    # Verificar si el archivo existe
    if not os.path.exists(icon_path):
        print(f"Advertencia: No se pudo encontrar el icono: {icon_path}")
        # Proporcionar un icono de respaldo
        fallback_icons = {
            'CONVIRTIENDO.gif': 'CONVERTIR-1.png',
            'NORMAL.png': 'ACEPTAR-1.png'
        }
        if icon_name in fallback_icons:
            return get_icon_path(fallback_icons[icon_name])
    
    return icon_path

class AnimatedButton(QPushButton):
    def __init__(self, icon_name, tooltip, click_handler=None, parent=None):
        super().__init__(parent)
        self.setStyleSheet("QPushButton {border: none; background: transparent;}")
        self._icon_size = 25
        self.setFixedSize(35, 35)
        icon_path = get_icon_path(icon_name)
        self.icon = QIcon(icon_path)
        self.setMouseTracking(True)
        self.setToolTip(tooltip)
        self.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        if click_handler:
            self.clicked.connect(click_handler)
        
        self.add_shadow_effect(Qt.GlobalColor.white)

    def add_shadow_effect(self, color):
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(color)
        self.setGraphicsEffect(shadow)

    @pyqtProperty(float)
    def icon_size(self):
        return self._icon_size

    @icon_size.setter
    def icon_size(self, size):
        self._icon_size = size
        self.update()

    def enterEvent(self, event):
        self.animate_icon_size(30)

    def leaveEvent(self, event):
        self.animate_icon_size(25)

    def animate_icon_size(self, target_size):
        self.animation = QPropertyAnimation(self, b"icon_size")
        self.animation.setDuration(100)
        self.animation.setStartValue(self.icon_size)
        self.animation.setEndValue(target_size)
        self.animation.start()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)

        icon_rect = QRectF((self.width() - self._icon_size) / 2,
                           (self.height() - self._icon_size) / 2,
                           self._icon_size, self._icon_size)
        self.icon.paint(painter, icon_rect.toRect())

def crear_boton_aceptar(self):
    self.acceptButton = crear_boton_play_svg(self)
    return self.acceptButton

def crear_boton_stop(self):
    self.stopButton = crear_boton_stop_svg(self)
    return self.stopButton

def crear_boton_limpiar(self):
    self.limpiarButton = crear_boton_ajustes_svg(self)
    return self.limpiarButton

def crear_boton_salvar(self):
    self.salvarButton = crear_boton_salvar_svg(self)
    return self.salvarButton

def crear_boton_cargar(self):
    self.cargarButton = crear_boton_cargar_svg(self)
    return self.cargarButton

def crear_boton_borrar(self):
    self.borrarButton = crear_boton_borrar_svg(self)
    return self.borrarButton

# Clase de botón animado como en ZETACOPY
class AnimatedSVGButton(QPushButton):
    def __init__(self, icon_func, tooltip_text, parent=None):
        super().__init__(parent)
        self.setFlat(True)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setFixedSize(28, 28)  # Más pequeño como en ZETACOPY
        self._tooltip_text = tooltip_text
        self._tooltip = None
        
        # Configurar tamaños de icono
        self._icon_size = 20
        self._hover_size = 24
        self.setIcon(icon_func(self._hover_size))
        self.setIconSize(QSize(self._icon_size, self._icon_size))
        
        # Configurar efectos de sombra
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(8)
        self.shadow.setXOffset(0)
        self.shadow.setYOffset(0)
        self.shadow.setColor(QColor(0, 0, 0, 160))
        self.setGraphicsEffect(self.shadow)
        
        # Estilo del botón
        self.setStyleSheet("""
            QPushButton {
                border: none;
                background: transparent;
            }
        """)

    def enterEvent(self, event):
        super().enterEvent(event)
        
        # Mostrar tooltip personalizado
        if self._tooltip_text:
            if not self._tooltip:
                from TOOLTIP_APARIENCIA import CustomToolTip
                self._tooltip = CustomToolTip(self._tooltip_text)
            self._tooltip.show_tooltip(self.mapToGlobal(QPoint(self.width()//2, 0)))
        
        # Cambiar a efecto de luz al hacer hover
        self.shadow.setColor(QColor(255, 255, 255, 100))
        self.shadow.setBlurRadius(15)
        
        # Animar a un tamaño más grande
        self.animate_icon_size(self._hover_size)

    def leaveEvent(self, event):
        super().leaveEvent(event)
        
        # Ocultar tooltip
        if self._tooltip:
            self._tooltip.hide()
            
        # Restaurar efecto de sombra normal
        self.shadow.setColor(QColor(0, 0, 0, 160))
        self.shadow.setBlurRadius(8)
        
        # Volver al tamaño original
        self.animate_icon_size(self._icon_size)

    def animate_icon_size(self, target_size):
        # Crear una nueva animación cada vez
        self.animation = QPropertyAnimation(self, b"iconSize")
        self.animation.setDuration(100)
        self.animation.setStartValue(self.iconSize())
        self.animation.setEndValue(QSize(target_size, target_size))
        self.animation.start()

# Nuevas funciones con iconos SVG y animaciones
def crear_boton_play_svg(parent=None):
    """Crea botón de play con icono SVG"""
    return AnimatedSVGButton(boton_PLAY, "Iniciar Conversión", parent)

def crear_boton_stop_svg(parent=None):
    """Crea botón de stop con icono SVG"""
    return AnimatedSVGButton(boton_STOP, "Detener Conversión", parent)

def crear_boton_ajustes_svg(parent=None):
    """Crea botón de ajustes con icono SVG"""
    return AnimatedSVGButton(boton_AJUSTES, "Configuración", parent)

def crear_boton_borrar_svg(parent=None):
    """Crea botón de borrar con icono SVG"""
    return AnimatedSVGButton(boton_BORRAR, "Borrar Archivos", parent)

def crear_boton_salvar_svg(parent=None):
    """Crea botón de salvar con icono SVG"""
    from CREAR import boton_SALVAR
    return AnimatedSVGButton(boton_SALVAR, "Guardar Sesión", parent)

def crear_boton_cargar_svg(parent=None):
    """Crea botón de cargar con icono SVG"""
    from CREAR import boton_CARGAR
    return AnimatedSVGButton(boton_CARGAR, "Cargar Sesión", parent)

def crear_boton_carpeta_svg(parent=None):
    """Crea botón de carpeta con icono SVG"""
    from CREAR import icono_CARPETA
    return AnimatedSVGButton(icono_CARPETA, "Convertir en Carpeta", parent)

def crear_boton_archivo_svg(parent=None):
    """Crea botón de archivo con icono SVG"""
    from CREAR import icono_OTROS
    return AnimatedSVGButton(icono_OTROS, "Convertir solo Archivo", parent)

def crear_boton_confirmar_svg(parent=None):
    """Crea botón de confirmar con icono SVG"""
    from CREAR import boton_CONFIRMAR
    button = AnimatedSVGButton(boton_CONFIRMAR, "Confirmar", parent)
    button.setFixedSize(35, 35)  # Más grande para ajustes
    button._icon_size = 25
    button._hover_size = 30
    button.setIconSize(QSize(button._icon_size, button._icon_size))
    return button

def crear_boton_cerrar_svg(parent=None):
    """Crea botón de cerrar con icono SVG"""
    from CREAR import boton_CERRAR
    button = AnimatedSVGButton(boton_CERRAR, "Cerrar", parent)
    button.setFixedSize(35, 35)  # Más grande para ajustes
    button._icon_size = 25
    button._hover_size = 30
    button.setIconSize(QSize(button._icon_size, button._icon_size))
    return button

def crear_boton_cancelar_svg(parent=None):
    """Crea botón de cancelar con icono SVG"""
    from CREAR import boton_BORRAR
    button = AnimatedSVGButton(boton_BORRAR, "Cancelar Conversión", parent)
    button.setFixedSize(16, 16)  # Tamaño pequeño para usar en listas
    button._icon_size = 14
    button._hover_size = 16
    button.setIconSize(QSize(button._icon_size, button._icon_size))
    return button