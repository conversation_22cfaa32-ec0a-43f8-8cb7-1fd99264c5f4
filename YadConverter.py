import datetime, shutil, sys, os, re, time, psutil, logging, multiprocessing, tempfile, subprocess, json, traceback, sys, uuid
from doble_clic import manejar_doble_clic
from CERRAR_APP import gestionar_cierre_aplicacion
# pyinstaller --onefile --add-data 'iconos/*;iconos/' YadCOnverter.py #ESTO PARA COMPILARLO
from PyQt6.QtWidgets import QApplication, QFileDialog, QLabel, QMessageBox, QListWidgetItem, QMessageBox, QSizeGrip, QStackedLayout, QAbstractItemView, QHBoxLayout, QWidget, QSpacerItem, QSizePolicy, QVBoxLayout, QComboBox, QCheckBox, QPushButton, QGraphicsDropShadowEffect
from PyQt6.QtCore import Qt, QSize, QEvent, QUrl, QThread, QThreadPool, QRunnable, pyqtSlot, pyqtSignal, QObject, QTimer, QPropertyAnimation, QEasingCurve, QAbstractAnimation
from PyQt6.QtGui import QIcon, QKeyEvent, QPainter, QColor, QFont, QPalette
from Ajustes import Ajustes
from FileListWidget import FileListWidget, CustomListWidgetItem
from destino import seleccionar_destino
from botones import crear_boton_play_svg, crear_boton_stop_svg, crear_boton_ajustes_svg, crear_boton_borrar_svg, crear_boton_salvar_svg, crear_boton_cargar_svg
from threading import Event
from queue import Queue
from ctypes import windll, byref, sizeof, c_int # PARA LOS BORDES REDONDEADOS DE LA VENTANA
from SALVA import on_salvarButton_clicked, on_cargarButton_clicked
from borrar_archivo import on_borrarButton_clicked, borrar_archivo_seleccionado
from DockSuperior import DockSuperior
from BARRA_TOTAL import BarraTotal
from CARGAR_SECCION import mostrar_dialogo_carga, cargar_datos_conversion
from APARIENCIA import ACCENT_POLICY, WINDOWCOMPOSITIONATTRIBDATA, ACCENT_ENABLE_ACRYLICBLURBEHIND, WCA_ACCENT_POLICY, apply_acrylic_effect
from REDIMENSIONAR import WindowResizer
### os.environ["PATH"] += os.pathsep + r"D:\CONVERTER(PERFECTO)\ffmpeg-6.0" probar
import ctypes
import platform
def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def exception_hook(exctype, value, tb):
    error_msg = ''.join(traceback.format_exception(exctype, value, tb))
    print(error_msg)  # Imprime en consola
    with open('error_log.txt', 'a') as error_file:
        error_file.write(error_msg)
sys.excepthook = exception_hook

def get_ffmpeg_path():
    if getattr(sys, 'frozen', False):
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(os.path.abspath(__file__))
    ffmpeg_path = os.path.join(base_path, 'ffmpeg', 'ffmpeg.exe')
    if not os.path.exists(ffmpeg_path):
        raise FileNotFoundError(f"No se pudo encontrar FFmpeg en {ffmpeg_path}")
    return ffmpeg_path
ffmpeg_path = get_ffmpeg_path() # Obtén la ruta de FFmpeg
os.environ["PATH"] = os.path.dirname(ffmpeg_path) + os.pathsep + os.environ["PATH"] # Añade la ruta de FFmpeg al PATH del sistema

class ConverterSignals(QObject):
    conversion_completed = pyqtSignal(str, str)  # file_id, status
    conversion_progress = pyqtSignal(str, str, str, int, str, int)  # file_id, filename, remaining_time, percentage, total_time, speed
    conversion_error = pyqtSignal(str)
    process_started = pyqtSignal(str, object) # Nueva señal para emitir el file_path y el objeto process
    conversion_cancelled = pyqtSignal(str, str) # Nueva señal para indicar cancelación (file_id, status)

def get_base_path():
    if getattr(sys, 'frozen', False):
        return sys._MEIPASS
    return os.path.dirname(os.path.abspath(__file__)) 

def get_ffsubsync_path():
        base_path = get_base_path()
        ffsubsync_path = os.path.join(base_path, 'ffsubsync_files', 'ffsubsync.exe')
        if not os.path.exists(ffsubsync_path):
            raise FileNotFoundError(f"No se pudo encontrar ffsubsync en {ffsubsync_path}")
        return ffsubsync_path 

class ConverterRunnable(QRunnable):
    def __init__(self, file_path, output_dir, list_widget, file_id, processes, stop_event, individual_stop_event, parametros_conversion):
        super().__init__()
        self.file_path = file_path
        self.output_dir = output_dir
        self.list_widget = list_widget
        self.file_id = file_id  # Usar ID único en lugar de list_item
        self.subtitle_path = None
        self.processes = processes
        self.signals = ConverterSignals()
        self.original_file_name = os.path.basename(file_path)
        self.stop_event = stop_event 
        self.individual_stop_event = individual_stop_event  # Evento de parada individual
        self.parametros_conversion = parametros_conversion
        self.progress_bar = BarraTotal() 
        self.conversion_completed = False
        base_path = get_base_path()
        self.ffsubsync_path = get_ffsubsync_path()
        self.ffmpeg_path = get_ffmpeg_path()
    
    @pyqtSlot()
    def run(self):
        if getattr(sys, 'frozen', False):
            application_path = sys._MEIPASS
        else:
            application_path = os.path.dirname(os.path.abspath(__file__))
        print(f"Converting {self.file_path}")
        file_name, file_ext = os.path.splitext(self.original_file_name)
        output_file_extension = self.parametros_conversion.get('extension', file_ext)
        self.output_file_path = os.path.join(self.output_dir, f"{file_name}{output_file_extension}")
        subtitle_extensions = ['.srt', '.ass', '.ssa', '.sub', '.vtt']
        subtitle_path = None
        for ext in subtitle_extensions:
            potential_subtitle_path = os.path.join(os.path.dirname(self.file_path), f"{file_name}{ext}")
            if os.path.exists(potential_subtitle_path):
                subtitle_path = potential_subtitle_path
                self.subtitle_path = subtitle_path
                break
        if not os.path.exists(self.output_file_path):
            try:
                if subtitle_path:
                    print(f"Sincronizando subtítulos para: {self.file_path}")
                    with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as temp_subtitle:
                        temp_subtitle_path = temp_subtitle.name
                    print(f"Ruta de ffsubsync: {self.ffsubsync_path}")
                    ffsubsync_command = [
                        self.ffsubsync_path,
                        self.file_path,
                        '-i', subtitle_path,
                        '-o', temp_subtitle_path
                    ]
                    print(f"Ejecutando comando ffsubsync: {' '.join(ffsubsync_command)}")
                    result = subprocess.run(ffsubsync_command, capture_output=True, text=True, encoding='utf-8', errors='ignore')
                    if result.returncode == 0:
                        subtitle_path = temp_subtitle_path
                        print(f"Subtítulos sincronizados correctamente: {subtitle_path}")
                    else:
                        print(f"Error al sincronizar subtítulos: {result.stderr}")
                command = [
                    self.ffmpeg_path,
                    '-i', self.file_path
                ]
                video_filters = []
                if subtitle_path:
                    print(f"Archivo de subtítulos sincronizado: {subtitle_path}")
                    escaped_subtitle_path = subtitle_path.replace("\\", "\\\\").replace(":", "\\:")
                    subtitle_size = self.parametros_conversion['subtitle_size']
                    subtitle_color = self.parametros_conversion['subtitle_color']
                    subtitle_font = self.parametros_conversion['subtitle_font']
                    video_filters.append(f"subtitles='{escaped_subtitle_path}':force_style='Fontname={subtitle_font},Fontsize={subtitle_size},PrimaryColour={subtitle_color},Bold=1'")
                else:
                    print(f"No se encontró archivo de subtítulos para: {self.file_path}")
                if self.parametros_conversion.get('scale'):
                    video_filters.append(self.parametros_conversion['scale'])
                if video_filters:
                    command.extend(['-vf', ','.join(video_filters)])
                aspect_ratio = self.parametros_conversion.get('aspect_ratio', '16:9')
                if aspect_ratio == '16:9':
                    command.extend(['-aspect', '16:9'])
                elif aspect_ratio == '4:3':
                    command.extend(['-aspect', '4:3'])
                command.extend([
                    '-r', self.parametros_conversion.get('framerate', ''),
                    '-vcodec', self.parametros_conversion.get('vcodec', ''),
                    '-b:v', self.parametros_conversion.get('bitrate', ''),
                    '-acodec', self.parametros_conversion.get('acodec', ''),
                    '-b:a', self.parametros_conversion.get('audio_bitrate', ''),
                    '-ar', self.parametros_conversion.get('audio_rate', ''),
                    '-ac', self.parametros_conversion.get('audio_channels', ''),
                    self.output_file_path
                ])
                print(f"Comando FFmpeg: {' '.join(command)}")
                process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, universal_newlines=True, creationflags=subprocess.CREATE_NO_WINDOW)
                self.signals.process_started.emit(self.file_path, process) # Emitir la señal con el proceso
                total_duration = None
                start_time = time.time()
                for line in process.stdout:
                    print(line.strip())
                    if self.stop_event.is_set() or self.individual_stop_event.is_set():
                        print(f"Detectada señal de parada para {self.file_path}. Terminando proceso.")
                        process.terminate()
                        process.stdout.close() # Cierra el stdout para desbloquear el bucle de lectura
                        try:
                            process.wait(timeout=1) # Pequeña espera para que termine
                        except subprocess.TimeoutExpired:
                            process.kill() # Forzar si no termina
                            process.wait()
                        self.signals.conversion_cancelled.emit(self.file_id, "CANCELADO")
                        return # Salir inmediatamente del run()
                    if total_duration is None:
                        duration_match = re.search(r"Duration: (\d{2}):(\d{2}):(\d{2}.\d{2})", line)
                        if duration_match:
                            hours, minutes, seconds = map(float, duration_match.groups())
                            total_duration = hours * 3600 + minutes * 60 + seconds
                    time_match = re.search(r"time=(\d{2}):(\d{2}):(\d{2}.\d{2})", line)
                    if time_match and total_duration is not None:
                        hours, minutes, seconds = map(float, time_match.groups())
                        elapsed = hours * 3600 + minutes * 60 + seconds
                        current_time = time.time()
                        time_elapsed = current_time - start_time
                        if elapsed > 0 and time_elapsed > 0:
                            speed = elapsed / time_elapsed
                            remaining_time = (total_duration - elapsed) / speed
                        else:
                            remaining_time = 0
                            speed = 0
                        percentage = min(int((elapsed / total_duration) * 100), 99) if total_duration else 0
                        remaining_time_str = str(datetime.timedelta(seconds=int(remaining_time)))
                        total_time_str = str(datetime.timedelta(seconds=int(total_duration))) if total_duration else "00:00:00"
                        self.signals.conversion_progress.emit(
                            self.file_id, 
                            self.original_file_name, 
                            remaining_time_str, 
                            percentage, 
                            total_time_str, 
                            int(speed * 100)  # Convertimos la velocidad a un porcentaje
                        )
                process.wait()
                if process.returncode == 0:
                    total_time_str = str(datetime.timedelta(seconds=int(total_duration))) if total_duration else "00:00:00"
                    self.signals.conversion_progress.emit(self.file_id, self.original_file_name, "00:00:00", 100, total_time_str, 0)
                    self.signals.conversion_completed.emit(self.file_id, "COMPLETADO")
                    print(f"Conversion completed for {self.file_path}")
                else:
                    print(f"Error en la conversión: FFmpeg retornó código {process.returncode}")
                    self.signals.conversion_error.emit(f"Error en la conversión: FFmpeg retornó código {process.returncode}")
                if subtitle_path and subtitle_path != self.subtitle_path:
                    try:
                        os.unlink(subtitle_path)
                    except Exception as e:
                        print(f"Error al eliminar el archivo temporal de subtítulos: {str(e)}")
            except Exception as e:
                print(f"Error en la conversión: {str(e)}")
                self.signals.conversion_error.emit(str(e))
            finally:
                if not (self.stop_event.is_set() or self.individual_stop_event.is_set()):
                    self.signals.conversion_completed.emit(self.file_id, "ERROR" if process.returncode != 0 else "COMPLETADO")
        else:
            print(f"El archivo ya existe: {self.output_file_path}")
            self.signals.conversion_progress.emit(self.file_id, self.original_file_name, "00:00:00", 100, "00:00:00", 0)
            self.signals.conversion_completed.emit(self.file_id, "COMPLETADO")

import uuid
class AppDemo(QWidget):
    def __init__(self):
        super().__init__()
        
        # Añade esta verificación al principio de __init__
        if platform.system() == "Windows":
            if is_admin():
                print("La aplicación se está ejecutando como administrador.")
            else:
                print("ADVERTENCIA: La aplicación NO se está ejecutando como administrador. OpenHardwareMonitor podría fallar al iniciarse.")
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)  # Sin bordes
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)  # Hacer el fondo de la ventana transparente
        hwnd = int(self.winId())
        apply_acrylic_effect(hwnd)
        self.set_rounded_corners(hwnd)
        self.progress_bar_color = "#05B8CC"
        self.ventana_ajustes = Ajustes(self)
        self.ventana_ajustes.parametros_actualizados.connect(self.actualizar_parametros_conversion)
        self.ventana_ajustes.parametros_actualizados.connect(self.cambiar_modo_ventana)
        self.ventana_ajustes = None
        if getattr(sys, 'frozen', False):
            application_path = sys._MEIPASS
        else:
            application_path = os.path.dirname(os.path.abspath(__file__))
        self.threadpool = QThreadPool()
        self.threadpool.setMaxThreadCount(multiprocessing.cpu_count())
        self.processes = {}
        self.progress_animations = {}
        self.progress_timers = {}
        self.stop_event = Event() 
        self.individual_stop_events = {}  # Diccionario para eventos de parada individuales
        self.job_queue = Queue() 
        self.resize(1200, 800)
        self.setGeometry(100, 100, 500, 500)
        self.current_mode = "normal" # Inicializar current_mode
        
        # Establecer el icono de la ventana usando Logo_ZConver
        from CREAR import Logo_ZConver
        self.setWindowIcon(Logo_ZConver(32))
        
        mainLayout = QVBoxLayout()

        self.dock = QWidget(self)
        self.dock = DockSuperior(self)
        mainLayout.addWidget(self.dock)
        self.setLayout(mainLayout)
        self.cargar_configuracion_inicial()
        self.aplicar_color_barras_progreso()
        self.listWidget = FileListWidget(self)
        self.listWidget.setToolTip('Arratrar Archivos para Convertir')
        self.listWidget.setStyleSheet(""" 
            QListWidget {
                background: transparent;
                border-radius: 10px;
                outline: 0;  /* Elimina el contorno de foco del widget */
            }
            QListWidget::item {
                padding: 0px 2px;  /* Padding horizontal muy reducido para pegar más al borde */
                border-radius: 5px;  /* Bordes redondeados como BARRA_TOTAL */
                margin: 0px 1px;  /* Margen horizontal muy reducido */
                min-height: 22px;  /* Altura más compacta */
                max-height: 22px;  /* Altura fija para consistencia */
            }
            QListWidget::item:selected {
                background: rgba(128, 128, 128, 120);  /* Gris semitransparente */
                border-radius: 5px;  /* Bordes redondeados como BARRA_TOTAL */
                border: none;  /* Sin borde para apariencia más limpia */
                margin: 0px 0px;  /* Sin margen para barra más larga pegada al borde */
            }
            QListWidget::item:hover {
                background: rgba(128, 128, 128, 60);  /* Hover gris más sutil */
                border-radius: 5px;
                margin: 0px 0px;  /* Sin margen para barra más larga pegada al borde */
            }
            QListWidget::item:focus {
                outline: none;  /* Elimina el contorno de foco del item */
                border: none;   /* Elimina cualquier borde que pueda aparecer al enfocar */
            }
        """)
        mainLayout.addWidget(self.listWidget)
        mainLayout.setContentsMargins(4, 4, 4, 3)  # Márgenes horizontales iguales a los verticales del dock superior
        # Crear botones usando las funciones de botones.py
        self.acceptButton = crear_boton_play_svg(self)
        self.acceptButton.clicked.connect(self.on_acceptButton_clicked)
        
        self.stopButton = crear_boton_stop_svg(self)
        self.stopButton.clicked.connect(self.on_stopButton_clicked)
        
        self.limpiarButton = crear_boton_ajustes_svg(self)
        self.salvarButton = crear_boton_salvar_svg(self)
        self.cargarButton = crear_boton_cargar_svg(self)
        self.borrarButton = crear_boton_borrar_svg(self)
        from custom_combobox import create_destination_combobox, create_conversion_limit_combobox
        
        self.destinationComboBox = create_destination_combobox(self)
        self.destinationComboBox.activated.connect(self.on_destinationComboBox_activated)
        
        # ComboBox de límite de conversiones con icono (más pequeño)
        self.conversionLimitComboBox = create_conversion_limit_combobox(self)
        self.conversionLimitComboBox.activated.connect(self.on_conversionLimitComboBox_changed)
        # Crear widget contenedor con fondo semitransparente para la sección inferior
        self.bottom_dock = QWidget()
        self.bottom_dock.setFixedHeight(40)  # Más pequeño para pegar más al borde
        self.bottom_dock.setStyleSheet("""
            background-color: rgba(80, 80, 80, 120);
            border-radius: 8px;
            border: none;
        """)
        buttonLayout = QHBoxLayout(self.bottom_dock)
        buttonLayout.setSpacing(6)  # Menos espacio
        buttonLayout.setContentsMargins(12, 3, 12, 3)  # Márgenes verticales más pequeños
        buttonLayout.addWidget(self.acceptButton)
        buttonLayout.addWidget(self.stopButton)
        buttonLayout.addWidget(self.limpiarButton)
        buttonLayout.addWidget(self.salvarButton)
        buttonLayout.addWidget(self.cargarButton)
        buttonLayout.addWidget(self.borrarButton)
        buttonLayout.addWidget(self.destinationComboBox)
        buttonLayout.addWidget(self.conversionLimitComboBox)
        buttonLayout.addItem(QSpacerItem(0, 0, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        mainLayout.addWidget(self.bottom_dock)
        self.setLayout(mainLayout)
        self.output_dir = ""
        self.is_converting = False  
        self.resize(600, 600)
        self.setMinimumSize(600, 600)  
        self.listWidget.installEventFilter(self)
        self.transparent_widget = QWidget(self)# Crear un widget transparente que cubra toda la ventana
        #self.transparent_widget.setStyleSheet("background-color: rgba(128, 128, 128, 0.4);")  GRIS TRANSPARENTE
        self.transparent_widget.setStyleSheet("background-color: rgba(0, 0, 0, 0.2);")  # Negro semitransparente
        self.transparent_widget.setGeometry(self.rect())
        self.transparent_widget.lower()  # Moverlo al fondo
        self.listWidget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.listWidget.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.listWidget.itemDoubleClicked.connect(self.on_itemDoubleClicked)
        self.listWidget.setIconSize(QSize(0, 0))  # Sin iconos
        from botones import crear_boton_carpeta_svg
        self.folderToggleButton = crear_boton_carpeta_svg(self)
        self.folderToggleButton.clicked.connect(self.on_folderToggleButton_clicked)
        from custom_combobox import create_settings_combobox
        self.settingsComboBox = create_settings_combobox(self)
        self.settingsComboBox.activated.connect(self.on_settingsComboBox_activated)
        self.load_settings_to_combobox()
        self.settingsComboBox.setToolTip('Parametros para Convetir')
        self.cargar_nombres_archivos_json_en_combobox()  # Carga los nombres de los archivos en el ComboBox
        try:
            with open('settings.txt', 'r') as f:
                settings = json.load(f)
                self.output_dir = settings['output_dir']
                self.output_dir = str(settings.get('output_dir', ''))  # Asegúrate de que sea una cadena
                if self.output_dir:  # Solo añade si no está vacío
                    self.destinationComboBox.addItem(self.output_dir)
                    # Inicializar el widget de espacio en disco en el dock superior
                    if hasattr(self.dock, 'system_info_widget'):
                        self.dock.system_info_widget.set_disk_path(self.output_dir)
                conversion_limit = settings['conversion_limit']
                index = self.conversionLimitComboBox.findText(conversion_limit)
                if index != -1:
                    self.conversionLimitComboBox.setCurrentIndex(index)
                settings_name = settings['settings_name']
                index = self.settingsComboBox.findText(settings_name)
                if index != -1:
                    self.settingsComboBox.setCurrentIndex(index)
                index = self.destinationComboBox.findText(self.output_dir)
                if index != -1:
                    self.destinationComboBox.setCurrentIndex(index)
                # Cargar estado del botón carpeta/archivo
                from CREAR import icono_CARPETA, icono_OTROS
                create_folder_setting = settings.get("create_folder", True)  # Por defecto carpeta
                
                if create_folder_setting:
                    self.create_folder = True
                    self.folderToggleButton.setIcon(icono_CARPETA(24))
                    self.folderToggleButton._tooltip_text = 'Convertir en Carpeta'
                else:
                    self.create_folder = False
                    self.folderToggleButton.setIcon(icono_OTROS(24))
                    self.folderToggleButton._tooltip_text = 'Convertir solo Archivo'
        except FileNotFoundError:
            # Configuración por defecto: carpeta
            from CREAR import icono_CARPETA
            self.create_folder = True
            self.folderToggleButton.setIcon(icono_CARPETA(24))
            self.folderToggleButton._tooltip_text = 'Convertir en Carpeta'
        self.settingsComboBox.activated.connect(self.on_settingsComboBox_activated)
        self.valorSrtLabel = QLabel()
        self.valorSrtLabel.setStyleSheet("color: white; background: transparent; border: none;")
        buttonLayout.addWidget(self.valorSrtLabel)
        buttonLayout.addWidget(self.folderToggleButton) 
        valor = self.valorSrtLabel.text() 
        self.setWindowTitle(valor)
        buttonLayout.addWidget(self.settingsComboBox)
        self.listWidget.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.listWidget.itemClicked.connect(self.on_item_clicked)

        self.listWidget.valorSrtCalculated.connect(self.update_valor_srt_label)
        self.currently_converting = set()  # Usar set para mejor rendimiento
        self.file_id_to_item = {}  # Mapeo de file_id a item
        self.listWidget.itemMoved.connect(self.on_item_moved)
        self.limpiarButton.clicked.connect(self.mostrar_ventana_ajustes)
        self.salvarButton.clicked.connect(lambda: on_salvarButton_clicked(self))
        self.cargarButton.clicked.connect(lambda: on_cargarButton_clicked(self))
        self.borrarButton.clicked.connect(self.on_borrarButton_clicked)
        self.borrarButton.clicked.connect(self.handle_borrar_clicked)
        
        self.ventana_ajustes = Ajustes(self)
        self.listWidget.totalSizeChanged.connect(self.update_total_size)
        self.destinationComboBox.setCurrentText(self.output_dir)
        self.ventana_ajustes.parametros_actualizados.connect(self.cambiar_modo_ventana)
        self.ventana_ajustes.parametros_actualizados.connect(self.actualizar_parametros_conversion)
        self.destinationComboBox.setCurrentText(self.output_dir)
        self.parametros_conversion = {
            'scale': '',
            'aspect_ratio': '',
            'framerate': '',
            'vcodec': '',
            'bitrate': '',
            'acodec': '',
            'audio_bitrate': '',
            'audio_rate': '',
            'audio_channels': '',
            'extension': ''
        }
        QTimer.singleShot(0, self.check_pending_conversion)
        self.window_resizer = WindowResizer(self)
        self.ohm_process = None # Inicializar la variable para el proceso de OpenHardwareMonitor
        self.start_ohm() # Iniciar OpenHardwareMonitor al inicio de la aplicación

    def start_ohm(self):
        """Inicia OpenHardwareMonitor.exe en segundo plano de forma asíncrona."""
        # Usar QTimer para iniciar de forma asíncrona y no bloquear la UI
        QTimer.singleShot(2000, self._start_ohm_async)
    
    def _start_ohm_async(self):
        """Método interno para iniciar OHM de forma asíncrona"""
        base_path = getattr(sys, 'frozen', False) and sys._MEIPASS or os.path.dirname(os.path.abspath(__file__))
        ohm_path = os.path.join(base_path, 'OpenHardwareMonitor', 'OpenHardwareMonitor.exe')
        
        print(f"Intentando iniciar OpenHardwareMonitor desde: {ohm_path}")
        
        if os.path.exists(ohm_path):
            try:
                # Verificar si ya está ejecutándose usando psutil
                try:
                    import psutil
                    for proc in psutil.process_iter(['name']):
                        if 'OpenHardwareMonitor.exe' in proc.info['name']:
                            print("OpenHardwareMonitor ya está ejecutándose")
                            return
                except ImportError:
                    pass
                
                # Verificar si ya tenemos un proceso activo
                if hasattr(self, 'ohm_process') and self.ohm_process and self.ohm_process.poll() is None:
                    print("OpenHardwareMonitor ya está ejecutándose (proceso propio)")
                    return
                
                print("Iniciando OpenHardwareMonitor...")
                
                # Intentar iniciar con privilegios elevados si es posible
                try:
                    # Método 1: Usar runas para privilegios elevados
                    self.ohm_process = subprocess.Popen(
                        ['runas', '/user:Administrator', ohm_path],
                        creationflags=subprocess.CREATE_NO_WINDOW,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL
                    )
                    print(f"OpenHardwareMonitor iniciado con privilegios elevados, PID: {self.ohm_process.pid}")
                except Exception:
                    # Método 2: Inicio normal si no se pueden obtener privilegios
                    self.ohm_process = subprocess.Popen(
                        [ohm_path], 
                        creationflags=subprocess.DETACHED_PROCESS | subprocess.CREATE_NO_WINDOW,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL
                    )
                    print(f"OpenHardwareMonitor iniciado normalmente, PID: {self.ohm_process.pid}")
                
                # Verificar después de 3 segundos si realmente se inició
                QTimer.singleShot(3000, self._verify_ohm_started)
                
            except Exception as e:
                print(f"Error al iniciar OpenHardwareMonitor: {e}")
                # Intentar método alternativo usando el widget de sistema
                if hasattr(self.dock, 'system_info_widget'):
                    print("Intentando iniciar OHM desde el widget de sistema...")
                    self.dock.system_info_widget.start_ohm_if_needed()
        else:
            print(f"Error: OpenHardwareMonitor.exe no encontrado en {ohm_path}")
    
    def _verify_ohm_started(self):
        """Verifica si OpenHardwareMonitor se inició correctamente"""
        try:
            import psutil
            for proc in psutil.process_iter(['name', 'pid']):
                if 'OpenHardwareMonitor.exe' in proc.info['name']:
                    print(f"✓ OpenHardwareMonitor verificado ejecutándose con PID: {proc.info['pid']}")
                    return
        except ImportError:
            pass
        print("⚠ OpenHardwareMonitor no se detecta ejecutándose")

    def stop_ohm(self):
        """Detiene el proceso de OpenHardwareMonitor."""
        if self.ohm_process and self.ohm_process.poll() is None: # Si el proceso existe y aún está corriendo
            print("Intentando detener OpenHardwareMonitor...")
            try:
                self.ohm_process.terminate()
                # Dar un tiempo para que el proceso termine
                self.ohm_process.wait(timeout=5)
                print("OpenHardwareMonitor detenido.")
            except subprocess.TimeoutExpired:
                # Si no termina, forzar el cierre
                self.ohm_process.kill()
                self.ohm_process.wait()
                print("OpenHardwareMonitor forzado a detenerse.")
            except Exception as e:
                print(f"Error al detener OpenHardwareMonitor: {e}")
    
    def cleanup_threads(self):
        """Limpia todos los hilos y timers antes de cerrar la aplicación"""
        try:
            # Detener timers del dock superior
            if hasattr(self.dock, 'system_info_widget'):
                widget = self.dock.system_info_widget
                if hasattr(widget, 'clock_timer'):
                    widget.clock_timer.stop()
                if hasattr(widget, 'cpu_timer'):
                    widget.cpu_timer.stop()
                if hasattr(widget, 'disk_timer'):
                    widget.disk_timer.stop()
                if hasattr(widget, 'hide_ohm_timer'):
                    widget.hide_ohm_timer.stop()
                
                # Limpiar hilos de CPU y disco
                if hasattr(widget, 'cpu_thread') and widget.cpu_thread.isRunning():
                    widget.cpu_thread.quit()
                    widget.cpu_thread.wait(1000)
                if hasattr(widget, 'disk_thread') and widget.disk_thread.isRunning():
                    widget.disk_thread.quit()
                    widget.disk_thread.wait(1000)
                
                # Limpiar monitor de temperatura con hilos
                if hasattr(widget, 'temp_monitor'):
                    widget.temp_monitor.cerrar()
            
            # Detener OpenHardwareMonitor
            self.stop_ohm()
            
        except Exception as e:
            print(f"Error al limpiar hilos: {e}")
    
    def closeEvent(self, event):
        """Maneja el evento de cierre de la aplicación"""
        self.cleanup_threads()
        event.accept()

    def mousePressEvent(self, event):
        # Optimizar para reducir carga durante el arrastre
        if event.button() == Qt.MouseButton.LeftButton:
            self.window_resizer.handle_mouse_press(event)
        
    def mouseMoveEvent(self, event):
        # Solo procesar si realmente estamos arrastrando
        if hasattr(self.window_resizer, 'dragging') and self.window_resizer.dragging:
            self.window_resizer.handle_mouse_move(event)
        
    def mouseReleaseEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.window_resizer.handle_mouse_release(event)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.window_resizer.update_positions()
        self.transparent_widget.setGeometry(self.rect())
    
    def find_item_by_file_id(self, file_id):
        """Encuentra un item de la lista por su file_id"""
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            item_file_id = item.data(Qt.ItemDataRole.UserRole + 3)
            if item_file_id == file_id:
                return item
        print(f"ADVERTENCIA: No se encontró item con file_id {file_id[:8]}...")
        print("File_IDs disponibles:")
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            item_file_id = item.data(Qt.ItemDataRole.UserRole + 3)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            print(f"  - {item_file_id[:8] if item_file_id else 'None'}... -> {os.path.basename(file_path) if file_path else 'None'}")
        return None
    
    def get_file_id_from_item(self, item):
        """Obtiene el file_id de un item"""
        return item.data(Qt.ItemDataRole.UserRole + 3)
    
    def clean_job_queue(self):
        """Limpia la cola de trabajos eliminando archivos que ya no existen en la lista"""
        if self.job_queue.empty():
            return
        current_file_ids = set()
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            file_id = item.data(Qt.ItemDataRole.UserRole + 3)
            if file_id:
                current_file_ids.add(file_id)
        new_queue = Queue()
        temp_jobs = []
        while not self.job_queue.empty():
            temp_jobs.append(self.job_queue.get())
        for file_id, file_path in temp_jobs:
            if file_id in current_file_ids:
                new_queue.put((file_id, file_path))
            else:
                print(f"Eliminando de la cola archivo borrado: {os.path.basename(file_path)}")
        self.job_queue = new_queue
        print(f"Cola limpiada: {self.job_queue.qsize()} archivos restantes")

    def get_combobox_style(self): #-------------------------------------------------------------------------------------#
        return """
            QComboBox {
                border: none;
                border-radius: 12px;
                padding: 2px 8px;
                background-color: rgba(40, 40, 40, 220);
                color: white;
                min-width: 45px;
                max-width: 85px;
                font-size: 10px;
                font-weight: bold;
                height: 22px;
            }
            QComboBox::drop-down {
                border: none;
                background-color: transparent;
                width: 0px;
            }
            QComboBox::down-arrow {
                image: none;
                width: 0px;
                height: 0px;
            }
            QComboBox::item {
                padding: 5px 8px;
                background-color: rgba(40, 40, 40, 220);
                color: white;
                border: none;
            }
            QComboBox::item:selected {
                background-color: rgba(0, 120, 212, 200);
                color: white;
            }
            QComboBox:hover {
                background-color: rgba(60, 60, 60, 220);
            }
            QComboBox QAbstractItemView {
                border: none;
                border-radius: 8px;
                background-color: rgba(40, 40, 40, 240);
                selection-background-color: rgba(0, 120, 212, 200);
                outline: none;
            }
        """

    def cargar_configuracion_inicial(self):
        log_file = os.path.join(os.path.dirname(sys.executable), 'debug_config_load.log')
        logging.basicConfig(filename=log_file, level=logging.DEBUG, 
                            format='%(asctime)s - %(levelname)s: %(message)s')
        logging.info("Iniciando carga de configuración")
        if getattr(sys, 'frozen', False):
            base_path = os.path.dirname(sys.executable)
        else:
            base_path = os.path.dirname(os.path.abspath(__file__))
        logging.info(f"Base path: {base_path}")
        config_path = os.path.join(base_path, 'config.txt')
        logging.info(f"Ruta del archivo de configuración: {config_path}")
        logging.basicConfig(filename=log_file, level=logging.DEBUG, 
                        format='%(asctime)s - %(levelname)s: %(message)s')
        logging.info(f"Base path: {base_path}")
        logging.info(f"Ruta del archivo de configuración: {config_path}")
        if os.path.exists(config_path):
            logging.info("Archivo de configuración encontrado")
            try:
                with open(config_path, 'r') as config_file:
                    config_content = config_file.read()
                    logging.info(f"Contenido del archivo de configuración:\n{config_content}")
                    config = json.loads(config_content)
                mode = config.get('modo', 'oscuro')
                logging.info(f"Modo cargado: {mode}")
                self.progress_bar_color = config.get('progress_bar_color', '#0078D7')
                logging.info(f"Color de barra de progreso cargado: {self.progress_bar_color}")
                self.cambiar_modo_ventana({'modo': 'light' if mode == 'claro' else 'dark'})
                self.actualizar_color_barras_progreso(self.progress_bar_color)
                logging.info("Configuración aplicada exitosamente")
            except json.JSONDecodeError as e:
                logging.error(f"Error al decodificar JSON: {str(e)}")
                self.cambiar_modo_ventana({'modo': 'dark'})
            except Exception as e:
                logging.error(f"Error inesperado al cargar la configuración: {str(e)}")
                self.cambiar_modo_ventana({'modo': 'dark'})
        else:
            logging.warning(f"No se encontró el archivo de configuración en {config_path}")
            self.cambiar_modo_ventana({'modo': 'dark'})
        logging.info("Finalización de la carga de configuración")
    
    def aplicar_color_barras_progreso(self):
        self.actualizar_color_barras_progreso(self.progress_bar_color)
    
    def actualizar_color_barras_progreso(self, color):
        self.progress_bar_color = color
        print(f"Actualizando color de barras de progreso a: {color}")
        if hasattr(self, 'listWidget'):
            for i in range(self.listWidget.count()):
                item = self.listWidget.item(i)
                widget = self.listWidget.itemWidget(item)
                if isinstance(widget, CustomListWidgetItem):
                    widget.progress_bar.setColor(color)
            self.listWidget.update()
        if hasattr(self, 'dock'):
            self.dock.update_clock_widget_color(color)

    def cambiar_modo_ventana(self, parametros):
        print("Cambiando modo de ventana")
        if 'modo' in parametros:
            mode = parametros['modo']
            print(f"Modo seleccionado: {mode}")
            apply_acrylic_effect(self.winId().__int__(), mode)
            is_light_mode = mode == 'light'
            palette = QPalette()
            if is_light_mode:
                print("Aplicando modo claro")
                palette.setColor(QPalette.ColorRole.WindowText, QColor(0, 0, 0))
                palette.setColor(QPalette.ColorRole.Text, QColor(0, 0, 0))
                palette.setColor(QPalette.ColorRole.ButtonText, QColor(0, 0, 0))
                palette.setColor(QPalette.ColorRole.Window, QColor(255, 255, 255))
                text_color = "color: black;"
                list_style = """
                    QListWidget {
                        background-color: transparent;
                        color: black;
                        border: none;
                    }
                    QListWidget::item {
                        color: black;
                        padding: 0px 2px;
                        border-radius: 5px;
                        margin: 0px 1px;
                        min-height: 22px;
                        max-height: 22px;
                    }
                    QListWidget::item:selected {
                        background-color: rgba(128, 128, 128, 120);
                        color: black;
                        border-radius: 5px;
                        border: none;
                        margin: 0px 0px;
                    }
                    QListWidget::item:hover {
                        background-color: rgba(128, 128, 128, 60);
                        border-radius: 5px;
                        margin: 0px 0px;
                    }
                """
            else:
                print("Aplicando modo oscuro")
                palette.setColor(QPalette.ColorRole.WindowText, QColor(255, 255, 255))
                palette.setColor(QPalette.ColorRole.Text, QColor(255, 255, 255))
                palette.setColor(QPalette.ColorRole.ButtonText, QColor(255, 255, 255))
                palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53))
                text_color = "color: white;"
                list_style = """
                    QListWidget {
                        background-color: transparent;
                        color: white;
                        border: none;
                    }
                    QListWidget::item {
                        color: white;
                        padding: 0px 2px;
                        border-radius: 5px;
                        margin: 0px 1px;
                        min-height: 22px;
                        max-height: 22px;
                    }
                    QListWidget::item:selected {
                        background-color: rgba(128, 128, 128, 120);
                        color: white;
                        border-radius: 5px;
                        border: none;
                        margin: 0px 0px;
                    }
                    QListWidget::item:hover {
                        background-color: rgba(128, 128, 128, 60);
                        border-radius: 5px;
                        margin: 0px 0px;
                    }
                """
            common_style = f"""
                QComboBox, QToolTip {{
                    {text_color}
                    background-color: {'#FFFFFF' if is_light_mode else '#333333'};
                }}
                QComboBox QAbstractItemView {{
                    {text_color}
                    background-color: {'#FFFFFF' if is_light_mode else '#333333'};
                }}
                QToolTip {{
                    border: 1px solid {'#CCCCCC' if is_light_mode else '#555555'};
                    padding: 2px;
                }}
            """
            QApplication.instance().setPalette(palette)
            self.setStyleSheet(f"""
                * {{ {text_color} }}
                {common_style}
                {list_style}
            """)
            if hasattr(self, 'listWidget'):
                self.listWidget.setStyleSheet(list_style)
            self.dock.update_text_color(is_light_mode)
            self.dock.update_background_color(is_light_mode)
            self.update_bottom_dock_color(is_light_mode)
            self.update()
            QApplication.processEvents()
            print("Paleta y estilos aplicados")
        if 'progress_bar_color' in parametros:
            self.actualizar_color_barras_progreso(parametros['progress_bar_color'])

    def update_bottom_dock_color(self, is_light_mode):
        """Actualiza el color de fondo del dock inferior según el modo de interfaz"""
        if hasattr(self, 'bottom_dock'):
            bg_color = "rgba(200, 200, 200, 120)" if is_light_mode else "rgba(80, 80, 80, 120)"
            self.bottom_dock.setStyleSheet(f"""
                background-color: {bg_color};
                border-radius: 8px;
                border: none;
            """)

    def move(self, pos):
        super().move(pos)
    
    def check_pending_conversion(self):
        print("Verificando conversión pendiente")
        project_dir = os.path.dirname(os.path.abspath(__file__))
        salva_dir = os.path.join(project_dir, 'SALVA')
        ultima_salva_path = os.path.join(salva_dir, 'ultima_salva.json')
        print(f"Buscando archivo: {ultima_salva_path}")
        if os.path.exists(ultima_salva_path):
            print("Archivo ultima_salva.json encontrado")
            archivo_seleccionado = mostrar_dialogo_carga(self, ultima_salva_path)
            if archivo_seleccionado:
                print(f"Archivo seleccionado: {archivo_seleccionado}")
                datos = cargar_datos_conversion(archivo_seleccionado)
                if datos:
                    self.cargar_datos_en_interfaz(datos)
            else:
                print("No se seleccionó ningún archivo")
        else:
            print("No se encontró el archivo ultima_salva.json")

    def cargar_datos_en_interfaz(self, datos):
        print("Cargando datos en la interfaz")
        print(f"Datos recibidos: {datos}")  # Imprime los datos para depuración
        self.listWidget.clear()
        large_bold_font = QFont()
        large_bold_font.setPointSize(9)  # Texto más pequeño consistente
        large_bold_font.setBold(True)
        if isinstance(datos, list):
            for archivo in datos:
                self.add_file_to_list(archivo, large_bold_font)
                print(f"Archivo añadido: {archivo}")
        elif isinstance(datos, dict):
            archivos = datos.get('files', [])
            for archivo in archivos:
                self.add_file_to_list(archivo, large_bold_font)
                print(f"Archivo añadido: {archivo}")
        else:
            print(f"Formato de datos no reconocido: {type(datos)}")
        formato_seleccionado = datos.get('selected_format') if isinstance(datos, dict) else None
        if formato_seleccionado:
            index = self.settingsComboBox.findText(formato_seleccionado)
            if index >= 0:
                self.settingsComboBox.setCurrentIndex(index)
                print(f"Formato seleccionado: {formato_seleccionado}")
        print("Datos de conversión cargados con éxito")

    def add_file_to_list(self, file_path, font, add_progress_bar=False):
        if file_path and os.path.exists(file_path):
            file_name = os.path.basename(file_path)
            file_name_to_display = os.path.splitext(file_name)[0]
            custom_widget = CustomListWidgetItem(file_name_to_display, file_path)
            custom_widget.setStatusIcon("PENDIENTE")  # Mostrar icono azul para archivos cargados pendientes
            custom_widget.label.setFont(font)
            item = QListWidgetItem(self.listWidget)
            from PyQt6.QtCore import QSize
            item.setSizeHint(QSize(-1, 23))  # 22px del widget + 1px de spacing
            item.setData(Qt.ItemDataRole.UserRole, file_path)
            item.setData(Qt.ItemDataRole.UserRole + 3, custom_widget.file_id)  # Guardar el ID único
            self.listWidget.addItem(item)
            self.listWidget.setItemWidget(item, custom_widget)
            custom_widget.progress_bar.hide()
        else:
            print(f"Archivo no encontrado o ruta inválida: {file_path}")

    def create_item_widget(self, item):
        widget = CustomListWidgetItem(item.text())
        widget.progress_bar.setValue(0)
        widget.progress_bar.setTextVisible(False)
        widget.progress_bar.hide()  # Ocultar inicialmente
        return widget
    
    def toggleMaximizeRestore(self):
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()
    
    def set_rounded_corners(self, hwnd):
        DWMWA_WINDOW_CORNER_PREFERENCE = 33
        DWMWCP_ROUND = 2
        windll.dwmapi.DwmSetWindowAttribute(hwnd, DWMWA_WINDOW_CORNER_PREFERENCE, byref(c_int(DWMWCP_ROUND)), sizeof(c_int))
    
    def cargar_icono(self, nombre_archivo, size=QSize(25, 25)):
        if getattr(sys, 'frozen', False):
            base_path = sys._MEIPASS
        else:
            base_path = os.path.dirname(os.path.abspath(__file__))
        ruta_icono = os.path.join(base_path, 'ICONOS', nombre_archivo)
        if os.path.exists(ruta_icono):
            icon = QIcon(ruta_icono)
            icon.addPixmap(icon.pixmap(size))  # Añade un pixmap escalado al icono
            return icon
        else:
            print(f"Advertencia: No se pudo encontrar el icono: {ruta_icono}")
            return QIcon()
    
    def load_settings_to_combobox(self):
        project_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"Directorio del proyecto: {project_dir}")
        for filename in os.listdir(project_dir):
            if filename.endswith('.json'):
                self.settingsComboBox.addItem(filename)
                print(f"Añadido al ComboBox: {filename}")
        self.settingsComboBox.addItem("...")  # Opción para abrir ajustes
    
    def seleccionar_destino(self):
        selected_dir = QFileDialog.getExistingDirectory(self, "Selecciona el directorio de destino")
        if selected_dir:
            if not selected_dir.endswith('/'):
                selected_dir += '/'
            self.output_dir = selected_dir
            if self.output_dir not in [self.destinationComboBox.itemText(i) for i in range(self.destinationComboBox.count())]:
                self.destinationComboBox.addItem(self.output_dir)
            self.destinationComboBox.setCurrentText(self.output_dir)
            if hasattr(self.dock, 'system_info_widget'):
                self.dock.system_info_widget.set_disk_path(self.output_dir)
        else:
            print("No se seleccionó ningún directorio.")
    
    def on_borrarButton_clicked(self):
        on_borrarButton_clicked(self.listWidget, self.currently_converting)

    def handle_borrar_clicked(self):
        on_borrarButton_clicked(self.listWidget, self.currently_converting)

    def update_conversion_queue(self):
        new_queue = Queue()
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            if "En cola" in item.text():  # Elemento en espera
                new_queue.put((i, file_path))
        self.job_queue = new_queue

    def rebuild_job_queue_after_move(self):
        """Reconstruye la cola de trabajos después de mover archivos"""
        print("Reconstruyendo cola de trabajos después del movimiento")
        if not self.is_converting:
            print("No hay conversiones activas, no se reconstruye la cola")
            return
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            file_id = item.data(Qt.ItemDataRole.UserRole + 3)
            if file_path and file_id:
                widget = self.listWidget.itemWidget(item)
                if isinstance(widget, CustomListWidgetItem):
                    if (hasattr(widget, 'icon_label') and widget.icon_label.isVisible() and
                        not (hasattr(widget, 'percentage_label') and widget.percentage_label.isVisible()) and
                        file_path not in self.currently_converting):
                        queue_items = []
                        temp_queue = Queue()
                        while not self.job_queue.empty():
                            queue_items.append(self.job_queue.get())
                        file_in_queue = any(item[0] == file_id for item in queue_items)
                        if not file_in_queue:
                            queue_items.append((file_id, file_path))
                            print(f"Agregado a la cola reconstruida: {os.path.basename(file_path)}")
                        for queue_item in queue_items:
                            temp_queue.put(queue_item)
                        self.job_queue = temp_queue
        self.check_and_start_conversions()

    def on_conversionLimitComboBox_changed(self):
        """Maneja el cambio en el límite de conversiones simultáneas"""
        new_limit = int(self.conversionLimitComboBox.currentText())
        current_converting = len(self.currently_converting)
        print(f"🔄 Límite de conversiones cambiado a: {new_limit}")
        print(f"📊 Actualmente convirtiendo: {current_converting}")
        if self.is_converting and current_converting < new_limit and not self.job_queue.empty():
            print(f"🚀 Iniciando conversiones adicionales ({new_limit - current_converting} más)")
            self.check_and_start_conversions()
        elif not self.is_converting and not self.job_queue.empty():
            print("🔄 No hay conversiones activas, pero hay archivos en cola")
    
    def check_and_start_conversions(self):
        conversion_limit = int(self.conversionLimitComboBox.currentText())
        print(f"Verificando conversiones: límite={conversion_limit}, convirtiendo={len(self.currently_converting)}, cola_vacía={self.job_queue.empty()}")
        if hasattr(self, 'job_queue') and self.job_queue:
            print(f"Cola de trabajos tiene {self.job_queue.qsize()} elementos")
        attempts = 0
        max_attempts = 10  # Evitar bucles infinitos
        while len(self.currently_converting) < conversion_limit and not self.job_queue.empty() and attempts < max_attempts:
            attempts += 1
            print(f"Intento {attempts} de iniciar nueva conversión")
            if not self.start_next_job():
                print("No se pudo iniciar la siguiente conversión, saliendo del bucle")
                break
        if self.job_queue.empty() and not self.currently_converting:
            self.is_converting = False
            print("No hay más archivos para convertir")
        else:
            self.is_converting = True
            print(f"Conversión activa: {len(self.currently_converting)} archivos convirtiendo, {self.job_queue.qsize()} en cola")
    
    def calcular_tamano_total(self):
        total_size = 0
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            if os.path.exists(file_path):
                total_size += os.path.getsize(file_path)
        return total_size / (1024 * 1024)  # Convertir a MB

    def update_total_size(self, total_size):
        if total_size >= 1024:  # Si el tamaño total supera los 1024MB, conviértelo a GB
            total_size /= 1024
            self.valorSrtLabel.setText(f"{total_size:.2f} GB")
        else:
            self.valorSrtLabel.setText(f"{total_size:.2f} MB")

    @pyqtSlot(QListWidgetItem)
    def on_item_clicked(self, item):
        total_size = 0
        file_count = 0
        for item in self.listWidget.selectedItems():
            file_path = item.data(Qt.ItemDataRole.UserRole)
            if file_path in self.currently_converting:
                item.setForeground(QColor(255, 255, 255))
            else:
                item.setForeground(QColor(255, 255, 255)) 
            specific_output_dir = self.get_output_dir(file_path)
            extension = self.parametros_conversion['extension']
            output_file_path = os.path.join(specific_output_dir, f"{os.path.splitext(os.path.basename(file_path))[0]}{extension}")
            if os.path.exists(output_file_path):
                file_size = os.path.getsize(output_file_path) / (1024 * 1024)  # Tamaño en MB
            else:
                file_size = os.path.getsize(file_path) / (1024 * 1024)  # Tamaño en MB del archivo original
            total_size += file_size
            file_count += 1
        if total_size >= 1024:  # Si el tamaño total supera los 1024MB, conviértelo a GB
            total_size /= 1024
            self.valorSrtLabel.setText(f"{total_size:.2f} GB | {file_count}")
        else:
            self.valorSrtLabel.setText(f"{total_size:.2f} MB | {file_count}")
        self.listWidget.update()
    
    @pyqtSlot(int) #-------------------------------REVISAR ESTO
    def update_valor_srt_label(self, valor_srt):
        self.valorSrtLabel.setText(f"TOTAL: {valor_srt} MB")

    def cargar_nombres_archivos_json_en_combobox(self):
        self.settingsComboBox.clear()  # Limpiar el ComboBox para evitar duplicados
        nombres_vistos = set()  # Conjunto para almacenar los nombres ya añadidos
        for archivo in os.listdir('.'):
            if archivo.endswith('.json'):
                nombre_sin_extension = archivo.replace('.json', '')
                if nombre_sin_extension not in nombres_vistos:
                    self.settingsComboBox.addItem(nombre_sin_extension)
                    nombres_vistos.add(nombre_sin_extension)  # Añadir al conjunto para rastrear
              
    def on_settingsComboBox_activated(self, index_or_name):
        if isinstance(index_or_name, int):
            filename = self.settingsComboBox.itemText(index_or_name)
        else:
            filename = index_or_name
        print(f"Archivo seleccionado: {filename}")
        if filename == "...":
            self.mostrar_ventana_ajustes()
            return
        if not filename.lower().endswith('.json'):
            filename += '.json'
        settings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), filename)
        print(f"Ruta completa del archivo: {settings_path}")
        print(f"El archivo existe: {os.path.exists(settings_path)}")
        if not os.path.exists(settings_path):
            print(f"El archivo de configuración '{settings_path}' no existe.")
            QMessageBox.warning(self, "Error", f"El archivo de configuración '{filename}' no existe.")
            return
        try:
            with open(settings_path, 'r', encoding='utf-8') as f:
                self.parametros_conversion = json.load(f)
            print(f"Contenido del archivo JSON: {self.parametros_conversion}")
            if isinstance(self.parametros_conversion, list) and len(self.parametros_conversion) > 0:
                self.parametros_conversion = self.parametros_conversion[0]
            if not isinstance(self.parametros_conversion, dict):
                raise ValueError("Los parámetros de conversión deben ser un diccionario")
            nueva_extension = self.parametros_conversion.get('extension', '')
            print(f"Nueva extensión: {nueva_extension}")
            for i in range(self.listWidget.count()):
                item = self.listWidget.item(i)
                file_path = item.data(Qt.ItemDataRole.UserRole)
                if file_path not in self.currently_converting:
                    base_file_name = os.path.splitext(os.path.basename(file_path))[0]
                    item.setText(f"{base_file_name}{nueva_extension}")
            print(f"Parámetros de conversión cargados: {self.parametros_conversion}")
        except json.JSONDecodeError as e:
            print(f"Error al decodificar el JSON: {str(e)}")
            QMessageBox.warning(self, "Error", f"El archivo '{filename}' no es un JSON válido: {str(e)}")
        except Exception as e:
            print(f"Error al cargar los parámetros de conversión: {str(e)}")
            QMessageBox.warning(self, "Error", f"No se pudieron cargar los parámetros de conversión: {str(e)}")
    
    def is_file_converting_or_queued(self, file_path):
        return file_path in self.currently_converting or self.is_file_in_queue(file_path)

    def is_file_in_queue(self, file_path):
        return any(job[1] == file_path for job in self.job_queue.queue)
    
    def agregar_archivos_a_cola(self, file_paths):
        extension_seleccionada = self.parametros_conversion.get('extension', '')
        for file_path in file_paths:
            if not self.is_file_converting_or_queued(file_path):
                file_id = str(uuid.uuid4())
                self.job_queue.put((file_id, file_path))
                item = QListWidgetItem(f"{os.path.splitext(os.path.basename(file_path))[0]}{extension_seleccionada}")
                item.setData(Qt.ItemDataRole.UserRole, file_path)
                item.setData(Qt.ItemDataRole.UserRole + 3, file_id)
                self.listWidget.addItem(item)
                
    def agregar_item_a_lista(self, file_path):
        nueva_extension = self.parametros_conversion.get('extension', '')
        base_file_name = os.path.splitext(os.path.basename(file_path))[0]
        file_id = str(uuid.uuid4())
        item = QListWidgetItem(f"{base_file_name}{nueva_extension}")
        item.setData(Qt.ItemDataRole.UserRole, file_path)
        item.setData(Qt.ItemDataRole.UserRole + 3, file_id)
        self.listWidget.addItem(item)

    def cargar_ajustes(self, nombre_archivo):
        try:
            with open(f'{nombre_archivo}.json', 'r') as config_file:
                return json.load(config_file)
        except (FileNotFoundError, json.JSONDecodeError):
            print(f"No se pudo cargar el archivo: {nombre_archivo}.json")
            return {}

    def load_settings_names(self):
        self.settingsComboBox.clear()
        self.settingsComboBox.addItem("Default")
        for ajuste in os.listdir('.'):
            if ajuste.endswith('.json'):
                self.settingsComboBox.addItem(os.path.splitext(ajuste)[0])
    
    def mostrar_ventana_ajustes(self):
        if not self.ventana_ajustes:
            self.ventana_ajustes = Ajustes(self)
            self.ventana_ajustes.parametros_actualizados.connect(self.actualizar_parametros_conversion)
            self.ventana_ajustes.parametros_actualizados.connect(self.cambiar_modo_ventana)
        self.ventana_ajustes.cargar_configuracion()
        geometry = self.geometry()
        self.ventana_ajustes.move(geometry.right(), geometry.top())
        self.ventana_ajustes.show()

    def actualizar_parametros_conversion(self, nuevos_parametros):
        print(f"Actualizando parámetros: {nuevos_parametros}")
        self.parametros_conversion.update(nuevos_parametros)
        if 'progress_bar_color' in nuevos_parametros:
            nuevo_color = nuevos_parametros['progress_bar_color']
            print(f"Nuevo color de barra de progreso: {nuevo_color}")
            self.actualizar_color_barras_progreso(nuevo_color)

    def actualizar_color_barras_progreso(self, color):
        self.progress_bar_color = color
        print(f"Actualizando color de barras de progreso a: {color}")
        if hasattr(self, 'listWidget'):
            for i in range(self.listWidget.count()):
                item = self.listWidget.item(i)
                widget = self.listWidget.itemWidget(item)
                if isinstance(widget, CustomListWidgetItem):
                    widget.progress_bar.setColor(color)
            self.listWidget.update()
        if hasattr(self, 'dock'):
            self.dock.update_clock_widget_color(color)

    def on_folderToggleButton_clicked(self):
        from CREAR import icono_CARPETA, icono_OTROS
        self.create_folder = not self.create_folder
        if self.create_folder:
            self.folderToggleButton.setIcon(icono_CARPETA(24))  # Tamaño hover
            self.folderToggleButton._tooltip_text = 'Convertir en Carpeta'
        else:
            self.folderToggleButton.setIcon(icono_OTROS(24))  # Tamaño hover
            self.folderToggleButton._tooltip_text = 'Convertir solo Archivo'
        if hasattr(self.folderToggleButton, '_tooltip') and self.folderToggleButton._tooltip:
            self.folderToggleButton._tooltip.hide()
            self.folderToggleButton._tooltip = None
            
    def on_folderCheckBox_stateChanged(self, state):
        self.create_folder = state == Qt.Checked
    
    def on_itemDoubleClicked(self, item):
        file_path = item.data(Qt.ItemDataRole.UserRole)
        manejar_doble_clic(file_path, self.output_dir, self.create_folder)

    def eventFilter(self, source, event): # MANEJO DEL TECLADO CON DELETE PARA BORRAR
        if event.type() == QEvent.Type.KeyPress:
            if event.key() == Qt.Key.Key_Delete:
                self.handle_delete_pressed()
                return True
        return super().eventFilter(source, event)
    def handle_borrar_clicked(self):
        on_borrarButton_clicked(self.listWidget, self.currently_converting)
    def handle_delete_pressed(self):
        borrar_archivo_seleccionado(self.listWidget, self.currently_converting, self.actualizar_contador)
    def actualizar_contador(self):
        count = self.listWidget.count()

    def on_acceptButton_clicked(self):
        print("Botón de aceptar pulsado")
        settings_name = self.settingsComboBox.currentText()
        self.on_settingsComboBox_activated(settings_name)
        extension = self.parametros_conversion['extension']
        file_paths = [self.listWidget.item(i).data(Qt.ItemDataRole.UserRole) for i in range(self.listWidget.count())]
        current_dir = os.path.dirname(os.path.realpath(__file__))
        salva_dir = os.path.join(current_dir, 'SALVA')
        os.makedirs(salva_dir, exist_ok=True)
        file_name = os.path.join(salva_dir, 'ultima_salva.json')
        with open(file_name, 'w') as f:
            json.dump(file_paths, f)
        if not self.is_converting:
            self.is_converting = True
            self.stop_event.clear()
            self.currently_converting.clear()  # Limpiar el set existente
            self.job_queue = Queue()
        large_bold_font = QFont()
        large_bold_font.setPointSize(9)  # Texto más pequeño consistente
        large_bold_font.setBold(True)
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            file_id = item.data(Qt.ItemDataRole.UserRole + 3)
            if not file_id:
                file_id = str(uuid.uuid4())
                item.setData(Qt.ItemDataRole.UserRole + 3, file_id)
                print(f"Asignando nuevo file_id {file_id[:8]}... al archivo {os.path.basename(file_path)}")
            file_name = os.path.splitext(os.path.basename(file_path))[0]
            base_text = f"{file_name}{extension}"
            specific_output_dir = self.get_output_dir(file_path)
            output_file_path = os.path.join(specific_output_dir, f"{file_name}{extension}")
            file_exists = os.path.exists(output_file_path)
            file_complete = False
            if file_exists:
                try:
                    with open(output_file_path, 'rb') as f:
                        f.seek(max(0, os.path.getsize(output_file_path) - 1024))
                        f.read(1024)
                    file_complete = True
                except:
                    file_complete = False
                    try:
                        os.remove(output_file_path)
                        print(f"Archivo corrupto eliminado: {output_file_path}")
                    except:
                        print(f"No se pudo eliminar el archivo corrupto: {output_file_path}")
            if file_complete:
                new_text = base_text  # Solo el nombre del archivo
                status_icon = "COMPLETADO"
                show_progress = False
            elif file_path in self.currently_converting:
                print(f"Archivo {os.path.basename(file_path)} ya está en conversión, manteniendo estado actual")
                continue
            else:
                new_text = base_text  # Solo el nombre del archivo
                status_icon = "EN_COLA"
                show_progress = True
                file_id = item.data(Qt.ItemDataRole.UserRole + 3)
                print(f"Agregando a la cola: file_id={file_id[:8]}..., file_path={os.path.basename(file_path)}")
                self.job_queue.put((file_id, file_path))
            item.setText("")
            if file_path not in self.currently_converting:
                widget = self.listWidget.itemWidget(item)
                if isinstance(widget, CustomListWidgetItem):
                    widget.label.setText(new_text)
                    widget.setStatusIcon(status_icon)  # Mostrar icono según el estado
                    if show_progress:
                        widget.progress_bar.setValue(0)
                        widget.progress_bar.show()
                    else:
                        widget.progress_bar.hide()
                else:
                    widget = CustomListWidgetItem(new_text, file_path)
                    widget.setStatusIcon(status_icon)  # Mostrar icono según el estado
                    if show_progress:
                        widget.progress_bar.setValue(0)
                        widget.progress_bar.show()
                    else:
                        widget.progress_bar.hide()
                    self.listWidget.setItemWidget(item, widget)
                widget.label.setFont(large_bold_font)
                self.apply_progress_bar_color(item)
        if not self.currently_converting:
            self.check_and_start_conversions()

    def start_conversion_for_item(self, item, index, file_path):
        self.apply_progress_bar_color(item)
    
    def get_output_dir(self, file_path):
        if self.window().create_folder:
            folder_name = os.path.basename(os.path.dirname(file_path))
            specific_output_dir = os.path.join(self.window().output_dir, folder_name)
            if not os.path.exists(specific_output_dir):
                os.makedirs(specific_output_dir)
        else:
            specific_output_dir = self.window().output_dir
        return specific_output_dir

    def start_next_job(self):
        if not self.job_queue.empty():
            file_id, file_path = self.job_queue.get()
            print(f"Obtenido de la cola: file_id={file_id[:8]}..., file_path={file_path}")
            item = self.find_item_by_file_id(file_id)
            if item:
                print(f"Item encontrado para file_id {file_id[:8]}...")
                if item.data(Qt.ItemDataRole.UserRole) == file_path:
                    print(f"File_path coincide, iniciando conversión para: {file_path}")
                    widget = self.listWidget.itemWidget(item)
                    if not isinstance(widget, CustomListWidgetItem):
                        print(f"El elemento con ID {file_id} no tiene un widget personalizado")
                        return False
                    if not (hasattr(widget, 'icon_label') and widget.icon_label.isVisible()):
                        print(f"El elemento con ID {file_id} ya no está en espera")
                        return False
                    file_name = os.path.splitext(os.path.basename(file_path))[0]
                    extension = self.parametros_conversion['extension']
                    specific_output_dir = self.get_output_dir(file_path)
                    output_file_path = os.path.join(specific_output_dir, f"{file_name}{extension}")
                    if os.path.exists(output_file_path):
                        try:
                            with open(output_file_path, 'rb') as f:
                                f.seek(max(0, os.path.getsize(output_file_path) - 1024))
                                f.read(1024)
                            print(f"El archivo ya existe y está completo: {output_file_path}")
                            self.update_item_status(file_id, "COMPLETADO")
                            return True
                        except:
                            try:
                                os.remove(output_file_path)
                                print(f"Archivo corrupto eliminado: {output_file_path}")
                            except:
                                print(f"No se pudo eliminar el archivo corrupto: {output_file_path}")
                    self.currently_converting.add(file_path)
                    base_text = f"{file_name}{extension}"
                    new_text = base_text
                    item.setText("")
                    widget.label.setText(new_text)
                    widget.setStatusIcon("CONVIRTIENDO")  # Mostrar icono de convirtiendo
                    widget.progress_bar.setValue(0)
                    widget.progress_bar.show()
                    large_bold_font = QFont()
                    large_bold_font.setPointSize(9)  # Texto más pequeño consistente
                    large_bold_font.setBold(True)
                    widget.label.setFont(large_bold_font)
                    individual_stop_event = Event()
                    self.individual_stop_events[file_path] = individual_stop_event
                    converter_runnable = ConverterRunnable(
                        file_path, 
                        specific_output_dir, 
                        self.listWidget, 
                        file_id, 
                        self.processes, 
                        self.stop_event, 
                        individual_stop_event,
                        self.parametros_conversion
                    )
                    converter_runnable.signals.conversion_completed.connect(self.update_item_status)
                    converter_runnable.signals.conversion_progress.connect(self.update_progress)
                    converter_runnable.signals.conversion_completed.connect(self.check_and_start_next_job)
                    converter_runnable.signals.conversion_error.connect(self.handle_conversion_error)
                    converter_runnable.signals.process_started.connect(self.handle_process_started) # Conectar la nueva señal
                    converter_runnable.signals.conversion_cancelled.connect(self.handle_conversion_cancelled) # Conectar la nueva señal de cancelación
                    print(f"Iniciando runnable para {file_path}")
                    self.threadpool.start(converter_runnable)
                    return True
                else:
                    print(f"ERROR: File_path no coincide. Esperado: {file_path}, Encontrado: {item.data(Qt.ItemDataRole.UserRole)}")
                    return False
            else:
                print(f"ERROR: No se encontró item para file_id {file_id[:8]}...")
                return False
        return False

    def on_item_moved(self):
        if self.is_converting:
            self.update_conversion_queue()
            self.check_and_start_conversions()
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            self.apply_progress_bar_color(item)
    def apply_progress_bar_color(self, item):
        widget = self.listWidget.itemWidget(item)
        if isinstance(widget, CustomListWidgetItem):
            widget.progress_bar.setBaseColor(self.progress_bar_color)

    def cancel_specific_file(self, file_path):
        """Cancela la conversión de un archivo específico"""
        print(f"Cancelando conversión de archivo específico: {file_path}")
        target_file_id = None
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            item_file_path = item.data(Qt.ItemDataRole.UserRole)
            if item_file_path == file_path:
                target_file_id = item.data(Qt.ItemDataRole.UserRole + 3)
                break
        if not target_file_id:
            print(f"No se encontró file_id para {file_path}")
            return
        if file_path in self.individual_stop_events:
            self.individual_stop_events[file_path].set()
            print(f"Evento de parada individual activado para: {file_path}")
        if file_path in self.processes:
            try:
                process = self.processes[file_path]
                if process.poll() is None:  # Si el proceso aún está ejecutándose
                    process.terminate()
                    try:
                        process.wait(timeout=3)
                        print(f"Proceso terminado graciosamente para: {file_path}")
                    except subprocess.TimeoutExpired:
                        process.kill()
                        process.wait()
                        print(f"Proceso forzado a terminar para: {file_path}")
                del self.processes[file_path]
            except Exception as e:
                print(f"Error al terminar el proceso para {file_path}: {e}")
        base_file_name = os.path.splitext(os.path.basename(file_path))[0]
        extension = self.parametros_conversion.get('extension', '')
        specific_output_dir = self.get_output_dir(file_path)
        converted_file_path = os.path.join(specific_output_dir, f"{base_file_name}{extension}")
        if os.path.exists(converted_file_path):
            try:
                os.remove(converted_file_path)
                print(f"Archivo en proceso eliminado: {converted_file_path}")
            except Exception as e:
                print(f"Error al eliminar el archivo en proceso {converted_file_path}: {e}")
        if file_path in self.currently_converting:
            self.currently_converting.remove(file_path)
        if file_path in self.individual_stop_events:
            del self.individual_stop_events[file_path]
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            item_file_path = item.data(Qt.ItemDataRole.UserRole)
            if item_file_path == file_path:
                widget = self.listWidget.itemWidget(item)
                if isinstance(widget, CustomListWidgetItem):
                    file_name = os.path.splitext(os.path.basename(file_path))[0]
                    base_text = f"{file_name}{extension}"
                    widget.label.setText(base_text)
                    widget.setStatusIcon("PENDIENTE")  # Mostrar icono azul cuando se cancela
                    widget.hideConversionInfo()
                    widget.progress_bar.setValue(0)
                    widget.progress_bar.hide()
                    if hasattr(self.listWidget, 'file_data') and file_path in self.listWidget.file_data:
                        self.listWidget.file_data[file_path]['converted'] = False
                        print(f"Archivo {os.path.basename(file_path)} cancelado y vuelto a estado PENDIENTE")
                break
        self.check_and_start_conversions()

    def handle_conversion_error(self, error_message):
        print(f"Error en la conversión: {error_message}")
        # Buscar el archivo que causó el error y marcar con icono de error
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            widget = self.listWidget.itemWidget(item)
            if isinstance(widget, CustomListWidgetItem):
                file_path = item.data(Qt.ItemDataRole.UserRole)
                if file_path in self.currently_converting:
                    widget.setStatusIcon("ERROR")  # Mostrar icono de error
                    widget.hideConversionInfo()  # Ocultar info de conversión
                    widget.progress_bar.hide()
                    # Remover de currently_converting en caso de error
                    if file_path in self.currently_converting:
                        self.currently_converting.remove(file_path)
                    break
        self.check_and_start_conversions() # Reintentar si hay más en cola

    @pyqtSlot(str, str)
    def update_item_status(self, file_id, status):
        print(f"Actualizando estado del archivo {file_id} a {status}")
        item = self.find_item_by_file_id(file_id)
        if item:
            file_path = item.data(Qt.ItemDataRole.UserRole)
            if file_path:
                file_name = os.path.splitext(os.path.basename(file_path))[0]
                extension = self.parametros_conversion.get('extension', '')
                base_text = f"{file_name}{extension}"
                new_text = base_text
                item.setText("")
                widget = self.listWidget.itemWidget(item)
                if isinstance(widget, CustomListWidgetItem):
                    widget.label.setText(new_text)
                    widget.setStatusIcon(status)  # Establecer icono según el estado
                    widget.hideConversionInfo()  # Ocultar info de conversión al cambiar estado
                    font = QFont()
                    font.setBold(True)
                    font.setPointSize(9)  # Texto más pequeño consistente
                    widget.label.setFont(font)
                    if status == "COMPLETADO":
                        widget.progress_bar.setValue(100)
                        widget.progress_bar.hide()
                    elif status == "ERROR":
                        widget.progress_bar.setValue(0)
                        widget.progress_bar.hide()
                else:
                    widget = CustomListWidgetItem(new_text, file_path)
                    widget.setStatusIcon(status)  # Establecer icono según el estado
                    font = QFont()
                    font.setBold(True)
                    font.setPointSize(9)  # Texto más pequeño consistente
                    widget.label.setFont(font)
                    if status == "COMPLETADO":
                        widget.progress_bar.setValue(100)
                        widget.progress_bar.hide()
                    elif status == "ERROR":
                        widget.progress_bar.setValue(0)
                        widget.progress_bar.hide()
                    self.listWidget.setItemWidget(item, widget)
                if file_path in self.currently_converting:
                    self.currently_converting.remove(file_path)
                    print(f"Eliminado {file_path} de currently_converting")
                    print(f"Archivos aún convirtiendo: {len(self.currently_converting)}")
                self.check_and_start_conversions()
            else:
                print(f"El elemento con ID {file_id} no tiene un archivo asociado")
        else:
            print(f"El elemento con ID {file_id} ya no existe")
        self.check_and_start_conversions()

    def check_and_start_next_job(self, file_id, status):
        print(f"Conversión completada para archivo {file_id} con estado {status}")
        self.update_item_status(file_id, status)
       
    @pyqtSlot(str, str, str, int, str, int)
    def update_progress(self, file_id, original_file_name, remaining_time, percentage, total_conversion_time, speed):
        item = self.find_item_by_file_id(file_id)
        if not item:
            return
        file_path = item.data(Qt.ItemDataRole.UserRole)
        if not file_path or os.path.basename(file_path) != original_file_name:
            return
        if file_path in self.individual_stop_events and self.individual_stop_events[file_path].is_set():
            print(f"Archivo {file_path} fue cancelado, ignorando actualización de progreso")
            return
        base_name = os.path.splitext(original_file_name)[0]
        new_extension = self.parametros_conversion.get('extension', '')
        base_text = f"{base_name}{new_extension}"
        item.setText("")
        widget = self.listWidget.itemWidget(item)
        if not isinstance(widget, CustomListWidgetItem):
            widget = CustomListWidgetItem(base_text, file_path)
            self.listWidget.setItemWidget(item, widget)
        widget.label.setText(base_text)
        if percentage == 100:
            widget.setStatusIcon("COMPLETADO")
            widget.setConversionInfo(100, "Completado")  # Mostrar 100% antes de ocultar
            self.animate_progress_bar(widget.progress_bar, 100, file_path)
            widget.progress_bar.show()
            def hide_completion_info():
                widget.hideConversionInfo()
                widget.progress_bar.hide()
                if file_path in self.progress_animations:
                    self.progress_animations[file_path].stop()
                    del self.progress_animations[file_path]
                if file_path in self.progress_timers:
                    self.progress_timers[file_path].stop()
                    del self.progress_timers[file_path]
            completion_timer = QTimer()
            completion_timer.setSingleShot(True)
            completion_timer.timeout.connect(hide_completion_info)
            completion_timer.start(1000)  # 1 segundo para ver el 100%
        else:
            widget.setStatusIcon("CONVIRTIENDO")
            widget.setConversionInfo(percentage, remaining_time)
            self._setup_cancel_callback(widget, file_path)
            self.animate_progress_bar(widget.progress_bar, percentage, file_path)
            widget.progress_bar.show()
        font = QFont()
        font.setBold(True)
        font.setPointSize(9)
        widget.label.setFont(font)
        self.apply_progress_bar_color(item)


    def _setup_cancel_callback(self, widget, file_path):
        """Configura el callback de cancelación para un widget específico"""
        if not file_path or file_path is False:
            print(f"ADVERTENCIA: file_path inválido: {file_path}")
            return
        
        def cancel_callback():
            print(f"Callback de cancelación activado para: {file_path}")
            self.cancel_specific_file(file_path)
        widget.setCancelCallback(cancel_callback)

    def cancel_specific_file(self, file_path):
        """Cancela la conversión de un archivo específico"""
        print(f"Cancelando conversión de archivo específico: {file_path}")
        if not file_path or file_path is False:
            print(f"ERROR: file_path inválido: {file_path}")
            return
        target_file_id = None
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            item_file_path = item.data(Qt.ItemDataRole.UserRole)
            if item_file_path == file_path:
                target_file_id = item.data(Qt.ItemDataRole.UserRole + 3)
                print(f"Encontrado file_id: {target_file_id[:8]}... para archivo: {os.path.basename(file_path)}")
                break
        if not target_file_id:
            print(f"No se encontró file_id para {file_path}")
            # Debug: mostrar todos los archivos disponibles
            print("Archivos disponibles en la lista:")
            for i in range(self.listWidget.count()):
                item = self.listWidget.item(i)
                item_file_path = item.data(Qt.ItemDataRole.UserRole)
                item_file_id = item.data(Qt.ItemDataRole.UserRole + 3)
                print(f"  - {os.path.basename(item_file_path) if item_file_path else 'None'} -> {item_file_id[:8] if item_file_id else 'None'}...")
            return
        if file_path in self.individual_stop_events:
            self.individual_stop_events[file_path].set()
            print(f"Evento de parada individual activado para: {file_path}")
        if file_path in self.processes:
            try:
                process = self.processes[file_path]
                if process.poll() is None:  # Si el proceso aún está ejecutándose
                    process.terminate()
                    try:
                        process.wait(timeout=3)
                        print(f"Proceso terminado graciosamente para: {file_path}")
                    except subprocess.TimeoutExpired:
                        process.kill()
                        process.wait()
                        print(f"Proceso forzado a terminar para: {file_path}")
                del self.processes[file_path]
            except Exception as e:
                print(f"Error al terminar el proceso para {file_path}: {e}")
        base_file_name = os.path.splitext(os.path.basename(file_path))[0]
        extension = self.parametros_conversion.get('extension', '')
        specific_output_dir = self.get_output_dir(file_path)
        converted_file_path = os.path.join(specific_output_dir, f"{base_file_name}{extension}")
        if os.path.exists(converted_file_path):
            try:
                os.remove(converted_file_path)
                print(f"Archivo en proceso eliminado: {converted_file_path}")
            except Exception as e:
                print(f"Error al eliminar el archivo en proceso {converted_file_path}: {e}")
        if file_path in self.currently_converting:
            self.currently_converting.remove(file_path)
        if file_path in self.individual_stop_events:
            del self.individual_stop_events[file_path]
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            item_file_path = item.data(Qt.ItemDataRole.UserRole)
            if item_file_path == file_path:
                widget = self.listWidget.itemWidget(item)
                if isinstance(widget, CustomListWidgetItem):
                    file_name = os.path.splitext(os.path.basename(file_path))[0]
                    base_text = f"{file_name}{extension}"
                    widget.label.setText(base_text)
                    widget.setStatusIcon("PENDIENTE")  # Mostrar icono azul cuando se cancela
                    widget.hideConversionInfo()
                    widget.progress_bar.setValue(0)
                    widget.progress_bar.hide()
                    if hasattr(self.listWidget, 'file_data') and file_path in self.listWidget.file_data:
                        self.listWidget.file_data[file_path]['converted'] = False
                        print(f"Archivo {os.path.basename(file_path)} cancelado y vuelto a estado PENDIENTE")
                break
        self.check_and_start_conversions()

    def animate_progress_bar(self, progress_bar, target_percentage, file_path=None):
        """Anima la barra de progreso de forma suave y precisa"""
        if not progress_bar or not hasattr(progress_bar, 'value'):
            return
        if file_path:
            if file_path in self.progress_animations:
                self.progress_animations[file_path].stop()
                del self.progress_animations[file_path]
            if file_path in self.progress_timers:
                self.progress_timers[file_path].stop()
                del self.progress_timers[file_path]
        try:
            current_value = progress_bar.value()
            if current_value == target_percentage:
                return
            if target_percentage == 100:
                progress_bar.setValue(100)
                print(f"🎯 Barra forzada al 100% inmediatamente")
            duration = 200 if target_percentage == 100 else 500
            animation = QPropertyAnimation(progress_bar, b"value")
            animation.setDuration(duration)
            animation.setStartValue(current_value)
            animation.setEndValue(target_percentage)
            animation.setEasingCurve(QEasingCurve.Type.OutCubic)  # Curva más suave
            if file_path:
                self.progress_animations[file_path] = animation
            animation.start()
        except RuntimeError:
            print(f"Error: Progress bar ya eliminada para {file_path}")

    @pyqtSlot(str, str, str, int, str, int)
    def update_progress(self, file_id, original_file_name, remaining_time, percentage, total_conversion_time, speed):
        item = self.find_item_by_file_id(file_id)
        if not item:
            return
        file_path = item.data(Qt.ItemDataRole.UserRole)
        if not file_path or os.path.basename(file_path) != original_file_name:
            return
        if file_path in self.individual_stop_events and self.individual_stop_events[file_path].is_set():
            print(f"Archivo {file_path} fue cancelado, ignorando actualización de progreso")
            return
        base_name = os.path.splitext(original_file_name)[0]
        new_extension = self.parametros_conversion.get('extension', '')
        base_text = f"{base_name}{new_extension}"
        item.setText("")
        widget = self.listWidget.itemWidget(item)
        if not isinstance(widget, CustomListWidgetItem):
            widget = CustomListWidgetItem(base_text, file_path)
            self.listWidget.setItemWidget(item, widget)
        widget.label.setText(base_text)
        if percentage == 100:
            widget.setStatusIcon("COMPLETADO")
            widget.setConversionInfo(100, "Completado")  # Mostrar 100% antes de ocultar
            self.animate_progress_bar(widget.progress_bar, 100, file_path)
            widget.progress_bar.show()
            def hide_completion_info():
                widget.hideConversionInfo()
                widget.progress_bar.hide()
                # Limpiar animaciones para este archivo específico
                if file_path in self.progress_animations:
                    self.progress_animations[file_path].stop()
                    del self.progress_animations[file_path]
                if file_path in self.progress_timers:
                    self.progress_timers[file_path].stop()
                    del self.progress_timers[file_path]
            completion_timer = QTimer()
            completion_timer.setSingleShot(True)
            completion_timer.timeout.connect(hide_completion_info)
            completion_timer.start(1000)  # 1 segundo para ver el 100%
        else:
            widget.setStatusIcon("CONVIRTIENDO")
            widget.setConversionInfo(percentage, remaining_time)
            self._setup_cancel_callback(widget, file_path)
            self.animate_progress_bar(widget.progress_bar, percentage, file_path)
            widget.progress_bar.show()
        font = QFont()
        font.setBold(True)
        font.setPointSize(9)
        widget.label.setFont(font)

    def on_stopButton_clicked(self):
        print("Botón de detener presionado")
        if not self.is_converting:
            return
        self.stop_event.set()
        extension = self.parametros_conversion['extension']
        for file_path, process in self.processes.items():
            print(f"Intentando detener el proceso para: {file_path}")
            try:
                parent = psutil.Process(process.pid)
                for child in parent.children(recursive=True):
                    child.terminate()
                parent.terminate()
                parent.wait(timeout=5)
                print(f"Proceso detenido para: {file_path}")
            except Exception as e:
                print(f"Error al terminar el proceso para {file_path}: {e}")
        time.sleep(2)
        for file_path in list(self.currently_converting):
            base_file_name = os.path.splitext(os.path.basename(file_path))[0]
            specific_output_dir = self.get_output_dir(file_path)
            converted_file_path = os.path.join(specific_output_dir, f"{base_file_name}{extension}")
            if os.path.exists(converted_file_path):
                for _ in range(5):
                    try:
                        os.remove(converted_file_path)
                        print(f"Archivo en proceso eliminado: {converted_file_path}")
                        break
                    except Exception as e:
                        print(f"Error al eliminar el archivo en proceso {converted_file_path}: {e}")
                        time.sleep(1)
            else:
                print(f"Archivo en proceso no encontrado: {converted_file_path}")
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            file_name = os.path.splitext(os.path.basename(file_path))[0]
            base_text = f"{file_name}{extension}"
            specific_output_dir = self.get_output_dir(file_path)
            output_file_path = os.path.join(specific_output_dir, f"{file_name}{extension}")
            file_exists = os.path.exists(output_file_path)
            item.setText("")
            widget = self.listWidget.itemWidget(item)
            if not isinstance(widget, CustomListWidgetItem):
                widget = CustomListWidgetItem(base_text)  
                self.listWidget.setItemWidget(item, widget)
                if file_exists:
                    widget.setStatusIcon("COMPLETADO")
                    widget.progress_bar.hide()
                continue
            if file_exists:
                new_text = base_text 
                widget.label.setText(new_text)
                widget.setStatusIcon("COMPLETADO")  
                widget.progress_bar.hide()
                print(f"Archivo ya completado: {file_path}")
            elif file_path in self.currently_converting:
                new_text = base_text  
                widget.label.setText(new_text)
                widget.setStatusIcon("EN_COLA")  
                widget.hideConversionInfo()  
                widget.progress_bar.setValue(0)
                widget.progress_bar.hide()
                print(f"Conversión interrumpida para: {file_path}, vuelve a la cola")
            else:
                new_text = base_text 
                widget.label.setText(new_text)
                widget.setStatusIcon("EN_COLA")  
                widget.hideConversionInfo()  
                widget.progress_bar.hide()
            font = QFont()
            font.setBold(True)
            font.setPointSize(9) 
            widget.label.setFont(font)
        for i in range(self.listWidget.count()):
            item = self.listWidget.item(i)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            if file_path and file_path in self.listWidget.file_data:
                specific_output_dir = self.get_output_dir(file_path)
                file_name = os.path.splitext(os.path.basename(file_path))[0]
                output_file_path = os.path.join(specific_output_dir, f"{file_name}{extension}")
                if not os.path.exists(output_file_path):
                    self.listWidget.file_data[file_path]['converted'] = False
                    print(f"Marcado como no convertido: {os.path.basename(file_path)}")
        self.currently_converting.clear()
        self.processes.clear()
        self.individual_stop_events.clear() # Limpiar también los eventos de parada individuales
        while not self.job_queue.empty():
            self.job_queue.get() # Vaciar la cola
        self.is_converting = False
        self.update()
        QApplication.processEvents()
        print("Conversión detenida y UI actualizada")

    def closeEvent(self, event):
        try:
            if hasattr(self, 'temp_monitor'):
                print("Cerrando monitor de temperatura...")
                self.temp_monitor.cerrar()
            self.stop_ohm() # Asegurarse de detener OpenHardwareMonitor al cerrar la app
            gestionar_cierre_aplicacion(
                self,
                event,
                self.output_dir,
                self.conversionLimitComboBox.currentText(),
                self.settingsComboBox.currentText(),
                self.create_folder,
                self.processes,
                self.is_converting
            )
        except Exception as e:
            print(f"Error al cerrar la aplicación: {e}")
            event.accept()  # Aceptar el cierre incluso si hay un error
    
    def on_destinationComboBox_activated(self, index):
        text = self.destinationComboBox.itemText(index)
        if text == "...":
            self.seleccionar_destino()
        else:
            self.output_dir = text
            if hasattr(self.dock, 'system_info_widget'):
                self.dock.system_info_widget.set_disk_path(self.output_dir)

    def handle_process_started(self, file_path, process):
        """Maneja la señal cuando un proceso de conversión se inicia."""
        self.processes[file_path] = process
        print(f"Proceso FFmpeg para {file_path} almacenado en AppDemo.processes")

    def handle_conversion_cancelled(self, file_id, status):
        """Maneja la señal cuando un proceso de conversión es cancelado."""
        print(f"Conversión cancelada para file_id: {file_id} con estado: {status}")
        item = self.find_item_by_file_id(file_id)
        if item:
            file_path = item.data(Qt.ItemDataRole.UserRole)
            widget = self.listWidget.itemWidget(item)
            if isinstance(widget, CustomListWidgetItem):
                display_status = "PENDIENTE" if status == "CANCELADO" else status
                widget.setStatusIcon(display_status) # Mostrar icono azul cuando se cancela
                widget.hideConversionInfo()
                widget.progress_bar.hide()
                if hasattr(self.listWidget, 'file_data') and file_path in self.listWidget.file_data:
                    self.listWidget.file_data[file_path]['converted'] = False
                    print(f"Archivo {os.path.basename(file_path)} cancelado y vuelto a estado PENDIENTE")
            if file_path in self.processes:
                del self.processes[file_path]
            if file_path in self.currently_converting:
                self.currently_converting.remove(file_path)
            self.check_and_start_conversions() # Intentar iniciar la siguiente conversión

    def setup_cpu_section(self, layout):
        """Configura la sección de la CPU para mostrar uso y temperatura"""
        cpu_widget = QWidget()
        cpu_widget.setStyleSheet("""
            background-color: rgba(40, 40, 40, 220);
            border-radius: 12px;
            border: none;
        """)
        cpu_widget.setFixedHeight(28)
        
        shadow = QGraphicsDropShadowEffect(cpu_widget)
        shadow.setBlurRadius(8)
        shadow.setXOffset(0)
        shadow.setYOffset(2)
        shadow.setColor(QColor(0, 0, 0, 100))
        cpu_widget.setGraphicsEffect(shadow)
        
        cpu_layout = QHBoxLayout(cpu_widget)
        cpu_layout.setContentsMargins(8, 0, 8, 0)
        cpu_layout.setSpacing(4)
        
        self.cpu_icon = QLabel()
        from CREAR import icono_CPU
        cpu_icon_pixmap = icono_CPU(16).pixmap(16, 16)
        self.cpu_icon.setPixmap(cpu_icon_pixmap)
        self.cpu_icon.setStyleSheet("background: transparent; border: none;")
        
        self.cpu_label = QLabel("CPU: --")
        self.cpu_label.setStyleSheet("color: rgba(255, 255, 255, 200); font-size: 11px; background: transparent; border: none;")
        self.cpu_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(cpu_widget)

app = QApplication(sys.argv)
app.setApplicationName("YADConverter")
demo = AppDemo()
demo.show()
sys.exit(app.exec())

