from PyQt6.QtWidgets import QSizeGrip
from PyQt6.QtCore import Qt, QMargins

class WindowResizer:
    def __init__(self, window):
        self.window = window
        self.dragging = False
        self.drag_position = None
        
        # Agregar grips para las esquinas y los bordes (sin bottom_right)
        self.size_grips = {
            'top_left': QSizeGrip(window),
            'top': QSizeGrip(window),
            'top_right': QSizeGrip(window),
            'right': QSizeGrip(window),
            'bottom': QSizeGrip(window),
            'bottom_left': QSizeGrip(window),
            'left': QSizeGrip(window)
        }
        
        grip_style = """
            QSizeGrip { 
                background: transparent; 
                width: 12px; 
                height: 12px;
            }
        """
        
        # Definir los cursores correctos para cada grip (sin bottom_right)
        self.cursors = {
            'top_left': Qt.CursorShape.SizeFDiagCursor,
            'top': Qt.CursorShape.SizeVerCursor,
            'top_right': Qt.CursorShape.SizeBDiagCursor,
            'right': Qt.CursorShape.SizeHorCursor,
            'bottom': Qt.CursorShape.SizeVerCursor,
            'bottom_left': Qt.CursorShape.SizeBDiagCursor,
            'left': Qt.CursorShape.SizeHorCursor
        }
        
        for pos, grip in self.size_grips.items():
            grip.setStyleSheet(grip_style)
            grip.setCursor(self.cursors[pos])  # Establecer el cursor correcto
            grip.raise_()
            grip.show()
            
        for widget in window.findChildren(QSizeGrip):
            if widget not in self.size_grips.values():
                widget.deleteLater()
                
        self._update_grip_positions()

    def handle_mouse_press(self, event):
        if event.button() == Qt.MouseButton.LeftButton and event.position().toPoint().y() <= 80:
            self.dragging = True
            self.drag_position = event.globalPosition().toPoint() - self.window.frameGeometry().topLeft()
            event.accept()
            return True
        return False

    def handle_mouse_move(self, event):
        if self.dragging:
            self.window.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
            return True
        return False

    def handle_mouse_release(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False
            event.accept()
            return True
        return False

    def _update_grip_positions(self):
        corner_size = 12  # Tamaño para las esquinas
        border_width = 8   # Ancho para los bordes laterales
        window_width = self.window.width()
        window_height = self.window.height()
        
        positions = {
            # Esquinas (sin bottom_right)
            'top_left': (0, 0, corner_size, corner_size),
            'top_right': (window_width - corner_size, 0, corner_size, corner_size),
            'bottom_left': (0, window_height - corner_size, corner_size, corner_size),
            
            # Bordes
            'top': (corner_size, 0, window_width - (corner_size * 2), border_width),
            'bottom': (corner_size, window_height - border_width, window_width - (corner_size * 2), border_width),
            'left': (0, corner_size, border_width, window_height - (corner_size * 2)),
            'right': (window_width - border_width, corner_size, border_width, window_height - (corner_size * 2))
        }
        
        for pos, grip in self.size_grips.items():
            grip.setGeometry(*positions[pos])

    def update_positions(self):
        self._update_grip_positions()
