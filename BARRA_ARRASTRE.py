from PyQt6.QtWidgets import QProgressBar
from PyQt6.QtGui import QPainter, QColor, QPainterPath, QPen
from PyQt6.QtCore import Qt, QRectF

class RoundedProgressBar(QProgressBar):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setTextVisible(False)
        self.setStyleSheet("""
            QProgressBar {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 7px;
            }
        """)
        self.color = QColor("#0078D7")  # Color por defecto
       
    def setColor(self, color):
        self.color = QColor(color)
        self.update()
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Dibuja el fondo
        bg_color = QColor(255, 255, 255, 25)  # Fondo semi-transparente
        painter.setBrush(bg_color)
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(self.rect(), 10, 10)

        # Calcula el ancho de la barra de progreso principal
        progress = self.value() / (self.maximum() - self.minimum())
        width = int(self.width() * progress)

        # Dibuja la barra de progreso principal
        if width > 0:
            painter.setBrush(self.color)
            
            if width < 14:  # Si el ancho es menor que el diámetro del círculo
                painter.drawEllipse(0, 0, 14, self.height())
            else:
                path = QPainterPath()
                path.addRoundedRect(QRectF(0, 0, width, self.height()), 10, 10)
                painter.drawPath(path)

        painter.end()